package com.sh.game.common.communication.msg.system.huoban;

import com.sh.game.common.communication.msg.abc.bean.CommonKeyValueBean;
import com.sh.game.common.communication.msg.system.huoban.bean.HuoBanBean;
import com.sh.game.common.communication.msg.system.huoban.bean.HuoBanFormationBean;
import com.sh.game.common.communication.msg.system.huoban.bean.RefreshHuoBanBean;
import com.sh.game.common.msg.RPC;
import com.sh.game.msg.AbsProtostuffMessage;
import java.lang.Override;
import java.util.ArrayList;
import java.util.List;

/**
 * 返回伙伴信息
 * 该文件由工具自动生成，不可修改
 */
@RPC("toClient")
public class ResHuoBanInfoMessage extends AbsProtostuffMessage {

    /**
     * 编队列表
     */
    private List<HuoBanFormationBean> formations = new ArrayList<>();

    /**
     * 正在使用的编队的索引
     */
    private int inUseFormation;

    /**
     * 召唤的伙伴信息
     */
    private List<HuoBanBean> huoBan = new ArrayList<>();

    /**
     * 总暴击增幅
     */
    private long criticalHit;

  @Override
  public int getId() {
    return 388004;
  }

  public void setFormations(List<HuoBanFormationBean> formations) {
    this.formations = formations;
  }

  public List<HuoBanFormationBean> getFormations() {
    return this.formations;
  }

  public void setInUseFormation(int inUseFormation) {
    this.inUseFormation = inUseFormation;
  }

  public int getInUseFormation() {
    return this.inUseFormation;
  }

  public void setHuoBan(List<HuoBanBean> huoBan) {
    this.huoBan = huoBan;
  }

  public List<HuoBanBean> getHuoBan() {
    return this.huoBan;
  }

  public void setCriticalHit(long criticalHit) {
    this.criticalHit = criticalHit;
  }

  public long getCriticalHit() {
    return this.criticalHit;
  }
}
