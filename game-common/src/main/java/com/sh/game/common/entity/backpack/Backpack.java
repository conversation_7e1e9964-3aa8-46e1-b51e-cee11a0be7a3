package com.sh.game.common.entity.backpack;


import com.sh.common.config.ConfigDataManager;
import com.sh.common.persist.Persistable;
import com.sh.game.common.config.model.ItemConfig;
import com.sh.game.common.constant.BagConst;
import com.sh.game.common.constant.DataType;
import com.sh.game.common.entity.backpack.constant.BackpackConst;
import com.sh.game.common.entity.backpack.item.Item;
import com.sh.game.common.entity.backpack.storage.Storage;
import com.sh.game.common.persist.PersistDaoTemplate;
import com.sh.game.common.persist.dao.PersistSimpleDao;
import io.protostuff.Exclude;
import io.protostuff.Tag;
import lombok.Getter;
import lombok.Setter;
import lombok.extern.slf4j.Slf4j;

import java.util.*;

/**
 * 背包类 其实是背包的大类 里面会分成各种类型的小背包
 */
@Getter
@Setter
@PersistDaoTemplate(PersistSimpleDao.class)
@Slf4j
public class Backpack implements Persistable {

    @Exclude
    private boolean dirty;

    @Exclude
    private long touchTime;

    @Override
    public int dataType() {
        return DataType.BACKPACK;
    }

    @Tag(1)
    private long id;

    /**
     * 货币
     */
    @Tag(2)
    private Map<Integer, Long> coin = new HashMap<>();

    /**
     * 各种类型的背包 <key:类型，value:背包小类>
     */
    @Tag(3)
    private Map<Integer, Storage> data = new HashMap<>();

    /**
     * 获取背包
     *
     * @param place
     * @return
     */
    public Storage fetchStorage(BackpackConst.Place place) {
        Storage storage = data.get(place.getWhere());
        if (storage == null) {
            switch (place) {
                case COIN:
                    break;
                default:
                    storage = new Storage();
                    data.put(place.getWhere(), storage);
                    break;
            }
        }

        if (storage != null) {
            storage.setPlace(place);
        }
        return storage;
    }

    /**
     * 获取背包
     *
     * @param where 背包类型
     * @return
     */
    public Storage fetchStorage(int where) {
        BackpackConst.Place place = BackpackConst.Place.getPlace(where);
        if (place == null) {
            return null;
        }

        return fetchStorage(place);
    }

    /**
     * 根据唯一id获取道具 范围是所有背包
     *
     * @param id
     * @return
     */
    public Item findItemByUniqueId(long id) {
        for (Map.Entry<Integer, Storage> entry : data.entrySet()) {
            for (Map.Entry<Integer, Item> itemEntry : entry.getValue().getData().entrySet()) {
                Item item = itemEntry.getValue();
                if (item != null && item.getId() == id) {
                    item.setWhere(entry.getKey());
                    item.setIndex(itemEntry.getKey());
                    return item;
                }
            }
        }

        return null;
    }

    public List<Item> findItemListByUniqueId(long id) {
        List<Item> list = new ArrayList<>();
        for (Map.Entry<Integer, Storage> entry : data.entrySet()) {
            for (Map.Entry<Integer, Item> itemEntry : entry.getValue().getData().entrySet()) {
                Item item = itemEntry.getValue();
                if (item != null && item.getId() == id) {
                    item.setWhere(entry.getKey());
                    item.setIndex(itemEntry.getKey());
                    list.add(item);
                }
            }
        }

        return list;
    }


    /**
     * 根据道具类型获取道具
     *
     * @param type
     * @param places 指定哪些背包
     * @return
     */
    public List<Item> findItemByItemType(int type, BackpackConst.Place... places) {
        if (places.length <= 0) {
            places = BackpackConst.Browse.BACKPACK;
        }
        List<Item> items = new ArrayList<>();
        for (BackpackConst.Place place : places) {
            Storage storage = fetchStorage(place);
            if (storage == null) {
                continue;
            }
            Set<Map.Entry<Integer, Item>> entries = storage.getData().entrySet();
            for (Map.Entry<Integer, Item> itemEntry : entries) {
                Item item = itemEntry.getValue();
                if (item != null) {
                    ItemConfig itemConfig = item.findItemConfig();
                    if (itemConfig != null && itemConfig.getItemtype() == type) {
                        item.setWhere(place.getWhere());
                        item.setIndex(itemEntry.getKey());
                        items.add(item);
                    }
                }
            }
        }
        return items;
    }

    /**
     * 根据装备类型获取道具
     *
     * @param type
     * @param places 指定哪些背包
     * @return
     */
    public List<Item> findItemByType(int type, BackpackConst.Place... places) {
        if (places.length <= 0) {
            places = BackpackConst.Browse.BACKPACK;
        }
        List<Item> items = new ArrayList<>();
        for (BackpackConst.Place place : places) {
            Storage storage = fetchStorage(place);
            if (storage == null) {
                continue;
            }
            Set<Map.Entry<Integer, Item>> entries = storage.getData().entrySet();
            for (Map.Entry<Integer, Item> itemEntry : entries) {
                Item item = itemEntry.getValue();
                if (item != null) {
                    ItemConfig itemConfig = item.findItemConfig();
                    if (itemConfig != null && itemConfig.getType() == type) {
                        item.setWhere(place.getWhere());
                        item.setIndex(itemEntry.getKey());
                        items.add(item);
                    }
                }
            }
        }
        return items;
    }

    /**
     * 获取指定背包指定id的道具
     *
     * @param id     道具唯一id
     * @param places 指定哪些背包
     * @return
     */
    public Item findItemByItemId(long id, BackpackConst.Place... places) {
        if (places.length <= 0) {
            places = BackpackConst.Browse.BACKPACK;
        }
        for (BackpackConst.Place place : places) {
            Storage storage = fetchStorage(place);
            if (storage == null) {
                continue;
            }
            Set<Map.Entry<Integer, Item>> entries = storage.getData().entrySet();
            for (Map.Entry<Integer, Item> itemEntry : entries) {
                Item item = itemEntry.getValue();
                if (item == null) continue;
                if (item.getId() == id) {
                    item.setWhere(place.getWhere());
                    item.setIndex(itemEntry.getKey());
                    return item;
                }

            }
        }
        return null;
    }

    /**
     * 获取指定id道具数量
     *
     * @param id
     * @param places 指定哪些背包
     * @return
     */
    public List<Item> findItemByCfgId(int id, BackpackConst.Place... places) {
        if (places.length <= 0) {
            places = BackpackConst.Browse.BACKPACK;
        }
        List<Item> items = new ArrayList<>();
        ItemConfig itemConfig = ConfigDataManager.getInstance().getById(ItemConfig.class, id);
        if (itemConfig == null) {
            return items;
        }
        int bind = itemConfig.getBind() == itemConfig.getId() ? 0 : itemConfig.getBind();
        for (BackpackConst.Place place : places) {
            Storage storage = fetchStorage(place);
            if (storage == null) {
                continue;
            }

            Set<Map.Entry<Integer, Item>> entries = storage.getData().entrySet();
            for (Map.Entry<Integer, Item> itemEntry : entries) {
                Item item = itemEntry.getValue();
                if (item != null) {
                    if (item.getCfgId() == id || bind == item.getCfgId()) {
                        item.setWhere(place.getWhere());
                        item.setIndex(itemEntry.getKey());
                        items.add(item);
                    }
                }
            }
        }

        return items;
    }

    /**
     * 获取指定配置类型数量
     * @param confId  cfgId
     * @param places  背包
     * @return  long 物品数量以long存储
     */
    public long fetchCountLByCfgId(int confId, BackpackConst.Place... places) {
        //默认背包获取
        if (places.length <= 0) {
            places = BackpackConst.Browse.BACKPACK;
        }
        long count = 0;

        ItemConfig itemConfig = ConfigDataManager.getInstance().getById(ItemConfig.class, confId);
        if (itemConfig == null) {
            //找不到道具返回0
            return count;
        }


        /**
         * 获取货币类型
         */
        if (itemConfig.getItemtype() == BagConst.ItemType.COIN) {
            Map<Integer, Long> coinMap = getCoin();
            count += coinMap.getOrDefault(confId, 0L);
            //需要排除自己绑定自己的情况 ,否则会出现实际货币值翻倍
            if (itemConfig.getBind() > 0 && itemConfig.getBind() != confId) {
                count += coinMap.getOrDefault(itemConfig.getBind(), 0L);
            }
            return count;
        }

        //绑定类型处理
        int bind = itemConfig.getBind() == itemConfig.getId() ? 0 : itemConfig.getBind();

        /**
         * 遍历背包查找
         */
        for (BackpackConst.Place place : places) {
            Storage storage = fetchStorage(place);
            if (storage == null) {
                continue;
            }
            for (Item item : storage.getData().values()) {
                if (item != null) {
                    if (item.getCfgId() == confId || bind == item.getCfgId()) {
                        count += item.findCountLong();
                    }
                }
            }
        }

        return count;
    }


    /**
     * 获取指定背包的指定格子道具
     *
     * @param place
     * @param index
     * @return
     */
    public Item fetchItemByIndex(BackpackConst.Place place, int index) {
        Item item = null;

        Storage storage = fetchStorage(place);
        if (storage != null) {
            item = storage.fetch(index);
        }
        if (item != null) {
            item.setWhere(place.getWhere());
        }

        return item;
    }


    /**
     * 校验道具是否足够
     *
     * @param items  道具列表
     * @param places 背包
     * @return 是否足够
     */
    public boolean verifyItemCount(List<int[]> items, BackpackConst.Place... places) {
        //默认背包获取
        if (places.length <= 0) {
            places = BackpackConst.Browse.BACKPACK;
        }

        Map<Integer, Integer> itemsMap = new HashMap<>();
        for (int[] item : items) {
            if (item.length < 2) {
                return false;
            }
            itemsMap.computeIfPresent(item[0], (k, v) -> v + item[1]);
            itemsMap.putIfAbsent(item[0], item[1]);
        }

        for (Map.Entry<Integer, Integer> entry : itemsMap.entrySet()) {
            ItemConfig itemConfig = ConfigDataManager.getInstance().getById(ItemConfig.class, entry.getKey());
            if (itemConfig == null) {
                //找不到道具返回false
                return false;
            }

            long count = 0;

            // 货币类型
            if (itemConfig.getItemtype() == BagConst.ItemType.COIN) {
                Map<Integer, Long> coinMap = getCoin();
                count += coinMap.getOrDefault(entry.getKey(), 0L);
                if (itemConfig.getBind() > 0) {
                    count += coinMap.getOrDefault(itemConfig.getBind(), 0L);
                }

                if (count < entry.getValue()) {
                    return false;
                }
            } else {
                //绑定类型处理
                int bind = itemConfig.getBind() == itemConfig.getId() ? 0 : itemConfig.getBind();

                // 遍历背包查找
                for (BackpackConst.Place place : places) {
                    Storage storage = fetchStorage(place);
                    if (storage == null) {
                        continue;
                    }
                    for (Item item : storage.getData().values()) {
                        if (item != null) {
                            if (item.getCfgId() == entry.getKey() || bind == item.getCfgId()) {
                                count += item.findCountLong();
                            }
                        }
                    }
                }

                if (count < entry.getValue()) {
                    return false;
                }
            }
        }
        return true;
    }


}
