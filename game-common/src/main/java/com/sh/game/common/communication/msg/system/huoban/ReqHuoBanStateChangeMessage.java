package com.sh.game.common.communication.msg.system.huoban;

import com.sh.game.common.msg.RPC;
import com.sh.game.msg.AbsProtostuffMessage;
import java.lang.Override;

/**
 * 请求更换伙伴上阵状态
 * 该文件由工具自动生成，不可修改
 */
@RPC("toLogic")
public class ReqHuoBanStateChangeMessage extends AbsProtostuffMessage {
  /**
   *  伙伴的配置id,下阵就传0
   */
  private int configId;

  /**
   * 上阵位置
   */
  private int index;

  /**
   * 队伍索引
   */
  private int linupIndex;

  @Override
  public int getId() {
    return 388002;
  }

  public void setConfigId(int configId) {
    this.configId = configId;
  }

  public int getConfigId() {
    return this.configId;
  }

  public void setIndex(int index) {
    this.index = index;
  }

  public int getIndex() {
    return this.index;
  }

  public void setLinupIndex(int linupIndex) {
    this.linupIndex = linupIndex;
  }

  public int getLinupIndex() {
    return this.linupIndex;
  }
}
