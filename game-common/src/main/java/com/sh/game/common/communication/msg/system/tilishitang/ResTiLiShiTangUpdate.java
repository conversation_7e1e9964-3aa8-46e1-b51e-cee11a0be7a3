package com.sh.game.common.communication.msg.system.tilishitang;

import com.sh.game.common.communication.msg.system.tilishitang.bean.PBTiLiBean;
import com.sh.game.common.msg.RPC;
import com.sh.game.msg.AbsProtostuffMessage;
import java.lang.Integer;
import java.lang.Override;
import java.util.ArrayList;
import java.util.List;

/**
 * 更新体力食堂变化数据信息
 * 该文件由工具根据 tilishitang.xml 文件自动生成，不可修改
 */
@RPC("toClient")
public class ResTiLiShiTangUpdate extends AbsProtostuffMessage {
  /**
   * 添加的体力
   */
  private List<PBTiLiBean> addTiLi = new ArrayList<>();

  /**
   * 删除的体力id
   */
  private List<Integer> delTiLi = new ArrayList<>();

  /**
   * 下一次体力恢复毫秒值
   */
  private long nextTiLiRecoverMill;

  /**
   * 下一次体力恢复数量
   */
  private int nextTiLiRecoverNum;

  @Override
  public int getId() {
    return 505003;
  }

  public void setAddTiLi(List<PBTiLiBean> addTiLi) {
    this.addTiLi = addTiLi;
  }

  public List<PBTiLiBean> getAddTiLi() {
    return this.addTiLi;
  }

  public void setDelTiLi(List<Integer> delTiLi) {
    this.delTiLi = delTiLi;
  }

  public List<Integer> getDelTiLi() {
    return this.delTiLi;
  }

  public void setNextTiLiRecoverMill(long nextTiLiRecoverMill) {
    this.nextTiLiRecoverMill = nextTiLiRecoverMill;
  }

  public long getNextTiLiRecoverMill() {
    return this.nextTiLiRecoverMill;
  }

  public void setNextTiLiRecoverNum(int nextTiLiRecoverNum) {
    this.nextTiLiRecoverNum = nextTiLiRecoverNum;
  }

  public int getNextTiLiRecoverNum() {
    return this.nextTiLiRecoverNum;
  }
}
