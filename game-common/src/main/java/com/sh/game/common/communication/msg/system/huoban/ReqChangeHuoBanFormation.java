package com.sh.game.common.communication.msg.system.huoban;

import com.sh.game.common.msg.RPC;
import com.sh.game.msg.AbsProtostuffMessage;
import java.lang.Override;

/**
 * 请求切换编队
 * 该文件由工具自动生成，不可修改
 */
@RPC("toLogic")
public class ReqChangeHuoBanFormation extends AbsProtostuffMessage {
  /**
   * 编队索引
   */
  private int linupIndex;

  @Override
  public int getId() {
    return 38805;
  }

  public void setLinupIndex(int linupIndex) {
    this.linupIndex = linupIndex;
  }

  public int getLinupIndex() {
    return this.linupIndex;
  }
}
