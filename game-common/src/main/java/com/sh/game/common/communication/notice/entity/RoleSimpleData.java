package com.sh.game.common.communication.notice.entity;

import lombok.Getter;
import lombok.Setter;

import java.util.HashMap;
import java.util.Map;

@Getter
@Setter
public class RoleSimpleData {

    /**
     * 角色id
     */
    private long rid;
    /**
     * 名字
     */
    private String name;
    /**
     * 等级
     */
    private int level;
    /**
     * 职业
     */
    private int career;
    /**
     * 性别
     */
    private int sex;
    /**
     * 头发
     */
    private int hair;
    /**
     * 帮会名字
     */
    private String unionName;
    /**
     * 装备
     */
    private Map<Integer, Integer> equips = new HashMap<>();
    /**
     * 时装
     */
    private Map<Integer, Integer> fashions = new HashMap<>();
}
