package com.sh.game.common.constant;

/**
 * <AUTHOR> <<EMAIL>>
 * @version 创建时间：2017年6月13日 下午9:07:06
 * global配置表的id字段
 */
public interface GameConst {

    interface GlobalId {
        /**
         * 跨服异常退出目的点
         */
        int CROSS_ERROR_BIRTH_POINT = 9002;
        /**
         * 跨服遗迹小怪
         */
        int CROSS_RUINS_NORMAL_MONSTER = 15604;
        /**
         * 神魔遗迹探险值最大上限值
         */
        int DEVIL_RUINS_MAX_EXPLORE = 28301;
        /**
         * 神魔遗迹 购买探险值消耗
         */
        int BUY_DEVIL_RUINS_EXPLORE_CONSUME = 28305;
        /**
         * 神魔遗迹 购买上限
         */
        int BUY_DEVIL_RUINS_EXPLORE_COUNT_LIMIT = 28306;

        /**
         * 世界花苑探险值最大上限值
         */
        int WORLD_GARDEN_MAX_EXPLORE = 28301;
        /**
         * 世界花园 购买探险值消耗
         */
        int BUY_WORLD_GARDEN_EXPLORE_CONSUME = 29304;
        /**
         * 世界花园 购买上限
         */
        int BUY_WORLD_GARDEN_EXPLORE_COUNT_LIMIT = 29305;
        /**
         * 邀请码不开放平台
         */
        int CREATE_ROLE_CAT_NOT_OPEN_PLATFORM = 30201;

        /**
         * 攻方地图ID1#X#Y#复活CD（城门破前）|攻方地图ID2#X#Y#复活CD（城门破后）|守方地图ID1#X#Y#复活CD（城门破前）|守方地图ID2#X#Y#复活CD（城门破后）
         */
        int SBK_REBORN = 1000;

        /**
         * 跨服沙巴克雕像
         */
        int SHABAKE_STATUE = 580021;

        /**
         * 跨服沙巴攻方地图
         */
        int CROSS_SBK_REBORN = 580022;

        /**
         * 沙巴克泡点
         */
        int SBK_PAODIAN = 1005;

        /**
         * 跨服沙巴克泡点
         */
        int CROSS_SBK_PAODIAN = 580023;

        /**
         * 沙巴克击杀积分
         */
        int SBK_KILL_SCORE = 1006;

        /**
         * 跨服沙巴克击杀积分
         */
        int CROSS_SBK_KILL_SCORE = 580024;

        /**
         * 召唤符持续时间内，召唤物死亡复活时间，单位（秒）
         */
        int ITEM_CALL_RELIVE_TIME = 1004;
        /**
         * 怪物地图数量限制-禁地封魔
         */
        int TREASURE_BOSS_LIMIT = 1008;

        /**
         * 社交列表上限
         */
        int SOCIAL_MAX = 100291;

        /**
         * 回城石回城点
         */
        int BACK_CITY_POINT = 100002;
        /**
         * 死亡万分比掉落全身耐久（万分比）
         */
        int RELIVE_COST_DURABLE = 100005;
        /**
         * 维修系数
         */
        int ITEM_REPAIR_FIX = 100009;
        /**
         * 精力倍数
         */
        int ENERGY_RATE = 100011;
        /**
         * 精力累计上限
         */
        int ENERGY_RECOVERY_MAX = 100012;
        /**
         * 精力值每日恢复量
         */
        int ENERGY_RECOVERY = 100013;
        /**
         * 累计经验上限（达到扣1点精力）
         */
        int ENERGY_EXP_MAX = 100014;

        /**
         * 新号精力初始值
         */
        int ENERGY_FIRST = 100015;

        /**
         * 组队人数限制
         */
        int TEAM_LIMIT_SIZE = 100023;

        /**
         * 行会申请保存时间
         */
        int UNION_APPLY_TIME = 100030;

        /**
         * 创建行会消耗
         */
        int UNION_CREATE_COST = 100031;

        /**
         * 行会创建成功公告
         */
        int UNION_CREATE_ANNOUNCE = 100032;

        /**
         * 退出行会后时间冷却
         */
        int UNION_OUT = 100033;

        /**
         * 行会大事件信息条目存储数量
         */
        int UNION_ENENT_NUM = 100038;

        /**
         * 行会会长转让邮件
         */
        int UNION_CHARMAN_CHANGE_MAIL = 100044;

        /**
         * 移动参数
         */
        int MOVE_PARAM = 100056;

        int UNION_DEFAULT_ANNOUNCE = 100260;

        /**
         * 公共CD毫秒 战#法#道
         */
        int COMMON_CD = 100057;
        /**
         * 死亡保护等级上限
         */
        int DIE_NOT_DROP_CONDITION = 100059;
        /**
         * PK值每多少秒清掉一点
         */
        int PK_DROP_TIME = 100060;

        /**
         * 神龙之魂特殊属性（PK时对神龙之魂等级低于自己的玩家造成额外减伤）
         */
        int DRAGON_WING_ATTR = 100210;

        int MIND_GHOST_ASSIGNS = 100203;
        /**
         * 结盟
         */
        int UNION_ENEMY_LAST = 100039;
        int UNION_ALIGN_LAST = 100040;

        int FUSION_BUY_DISCOUNT = 100053;

        /**
         * 经验宝箱兑换  宝箱ID#所需数量#每日兑换限制次数
         */
        int EXP_BOX_DAILY_TIME = 100271;

        /**
         * 英雄最大忠诚度
         */
        int MAX_LOYALTY = 100272;

        /**
         * 死亡扣除忠诚度
         */
        int DEAD_COST_LOYALTY = 100273;

        /**
         * 击杀怪物增加忠诚度
         */
        int KILL_MONSTER_ADD_LOYALTY = 100274;
        /**
         * 仓库不能放的类型
         */
        int WAREHOUSE_TYPE = 100276;
        /**
         * 矿石产出权值
         */
        int OREHOLE_RANDOM = 100285;

        /**
         * 挖矿一次时长 秒
         */
        int OREHOLE_ONE_TIME = 100286;
        /**
         * 挖矿地图
         */
        int OREHOLE_MAP = 100289;

        /**
         * 自动删除队伍时间，秒
         */
        int AUTO_REMOVE_TEAM_MINUTE = 100292;

        /**
         * 队伍邀请时间间隔，秒
         */
        int TEAM_INVITE_SPANCE_SECOND = 100293;

        /**
         * 合体技能能量槽最大值
         */
        int COMB_SKILL_VALUE_MAX = 100295;

        /**
         * 治疗使用间隔，秒
         */
        int CURE_TIME_INTERVAL = 100296;

        /**
         * 清除罪恶值消耗
         */
        int CLEAN_PKVALUE_COST = 100297;

        /**
         * 职业排名称号
         */
        int CAREER_RANK_FASHION = 100298;
        /**
         * 合体技单次消耗的能量
         */
        int COMB_SKILL_COST_VALUE = 100299;

        /**
         * 被申请好友所需条件
         */
        int FRIEND_APPLY_CONDITION = 100300;

        /**
         * 行会人数少于直接解散
         */
        int UNION_DISSOLVE_NUM = 100301;

        /**
         * 行会多于指定人数解散倒计时
         */
        int UNION_DISSOLVE_TIME = 100302;

        /**
         * 解散倒计时邮件
         */
        int UNION_DISSOLVE_START_MAIL = 100303;

        /**
         * 资金少于天数自动解散
         */
        int UNION_AUTO_DISSLOVE_DAY = 100305;

        /**
         * 自动解散邮件
         */
        int UNION_AUTO_DISSLOVE_MAIL = 100306;

        /**
         * 行会踢人邮件
         */
        int UNION_TICK_MAIL = 100307;

        /**
         * 行会正常解散邮件
         */
        int UNION_DISSOLVE_MAIL = 100309;

        /**
         * 矿洞自动出售特权
         */
        int OREHOLE_PREVILEGE = 100313;

        /**
         * 火龙之心免费充能次数
         */
        int HUO_LONG_FREE_COUNT = 100320;

        /**
         * 沙巴克修复数量
         */
        int SHABAKE_FIX_COST = 100403;

        /**
         * 沙巴克召唤数量
         */
        int SHABAKE_CALL_NUM = 100404;

        /**
         * 沙巴克城墙buff
         */
        int SHBAK_WALL_BUFF = 100405;

        /**
         * 沙巴克怪物
         */
        int SHABAKE_MONSTER = 100406;

        /**
         * 弓箭手id#带刀护卫id
         */
        int SHABAKE_PROTECT_ID = 100408;

        /**
         * 弓箭手坐标x#弓箭手坐标y#面向
         */
        int SHABAKE_GJ_POINT = 100409;

        /**
         * 带刀护卫坐标x#带刀护卫坐标y#面向
         */
        int SHABAKE_PROTECT_POINT = 100410;

        /**
         * 可以占领时间
         */
        int SHABAKE_CHANGE_TIME = 100411;

        /**
         * npc坐标
         */
        int SHABAKE_NPC_POINT = 100412;

        /**
         * 沙巴克称号
         */
        int SHABAKE_WIN_TITLE = 100416;

        /**
         * 跨服沙巴克修复数量
         */
        int CROSS_SHABAKE_FIX_COST = 580004;

        /**
         * 跨服沙巴克召唤数量
         */
        int CROSS_SHABAKE_CALL_NUM = 580005;

        /**
         * 跨服沙巴克城墙buff
         */
        int CROSS_SHBAK_WALL_BUFF = 580006;

        /**
         * 跨服沙巴克怪物
         */
        int CROSS_SHABAKE_MONSTER = 580007;

        /**
         * 跨服弓箭手id#带刀护卫id
         */
        int CROSS_SHABAKE_PROTECT_ID = 580009;

        /**
         * 跨服弓箭手坐标x#弓箭手坐标y#面向
         */
        int CROSS_SHABAKE_GJ_POINT = 580010;

        /**
         * 跨服带刀护卫坐标x#带刀护卫坐标y#面向
         */
        int CROSS_SHABAKE_PROTECT_POINT = 580011;

        /**
         * 跨服可以占领时间
         */
        int CROSS_SHABAKE_CHANGE_TIME = 580012;

        /**
         * 跨服npc坐标
         */
        int CROSS_SHABAKE_NPC_POINT = 580013;

        /**
         * 跨服沙巴克称号
         */
        int CROSS_SHABAKE_WIN_TITLE = 580017;

        /**
         * 跨服沙巴克称号邮件
         */
        int CROSS_SHABAKE_WIN_MAIL = 580031;
        /**
         * 主线挖矿
         */
        int OREHOLE_TASK = 100504;

        /**
         * 世界BOSS排名奖励
         */
        int WORLD_RANK_AWARD = 100506;
        /**
         * 世界BOSS参与奖励
         */
        int WORLD_PARTICIPATE_AWARD = 100507;
        /**
         * 创建行会条件
         */
        int CREATE_UNION_CONDITION = 100512;

        /**
         * 回城复活血量百分数
         */
        int HOME_RELIVE_HP_PERCENT = 100513;

        /**
         * 创角默认称号
         */
        int CREATE_DEFAULT_FASHION = 100514;

        /**
         * 特戒组
         */
        int SPECIAL_RING_GROUP = 100517;

        /**
         * 魔器组
         */
        int DEMON_EQUIP_GROUP = 100578;

        /**
         * 特戒合成组
         */
        int SPECIAL_RING_COMPOUND_GROUP = 100529;

        /**
         * 每日给道具
         */
        int DAILY_ITEM = 100519;

        /**
         * 开服天数#邀请码
         */
        int INVITATION_CODE = 100520;

        /**
         * 幻影魔王buff
         */
        int SHADOW_DEVIL_BUFF_ID = 100532;

        /**
         * 幻影魔王刷怪等待时间
         */
        int SHADOW_DEVIL_WAIT_TIME = 100533;

        /**
         * 幻影魔王幸运奖
         * 对应box表
         */
        int SHADOW_DEVIL_LUCK_AWARD = 100534;

        /**
         * 幻影魔王参与奖
         * 对应box表
         */
        int SHADOW_DEVIL_PARTICIPATE_AWARD = 100535;

        /**
         * 幻影魔王最大波数
         */
        int SHADOW_DEVIL_MAX_WAVE = 100537;

        /**
         * 幻影魔王出了4级技能书后，掉落该box的id  掉落
         */
        int SHADOW_DEVIL_LUCK_AWARD_B = 100538;

        /**
         * 幻影魔王出了4级技能书的id
         */
        int SHADOW_DEVIL_FOUR_LEVEL_SKILL = 100539;

        /**
         * 最大掉落日志数量
         */
        int MAX_DROP_RECORD_COUNT = 100542;

        /**
         * 神塔进入奖励
         */
        int SHEN_TA_ENTER_REWARD = 100540;

        /**
         * 神塔第一个地图id
         */
        int SHEN_TA_FIRST_MAPID = 100544;

        /**
         * 狂暴之力不移除的地图id #号分割
         */
        int VIOLENT_POWER_NOT_VANISH = 100548;

        /**
         * 丢弃道具到地图开关
         */
        int DISCARD_ITEM_COMMAND = 100549;

        /**
         * 最大每日邮件数量
         */
        int MAX_DAILY_MAIL_COUNT = 100550;

        /**
         * 重新分配阵营时间#重新分配阵营时间- 皇宫乱斗
         */
        int BRAWLING_RESTART_RANK = 100554;
        /**
         * 出生点1|出生点2- 皇宫乱斗
         */
        int BRAWLING_BORN = 100555;
        /**
         * 重新分配阵营时间- 皇宫乱斗
         */
        int BRAWLING_RESTART = 100556;
        /**
         * 泡点积分#泡点间隔- 皇宫乱斗
         */
        int BRAWLING_TIME_SCORE = 100557;
        /**
         * 击杀玩家获得积分#击杀怪获得积分#助攻获得积分#被击杀获得积分- 皇宫乱斗
         */
        int BRAWLING_SCORE = 100558;
        /**
         * buff刷新坐标- 皇宫乱斗
         */
        int BRAWLING_BUFF_POINT = 100559;
        /**
         * 复活的buff- 皇宫乱斗
         */
        int BRAWLING_RELIVE_BUFF = 100560;
        /**
         * 连续被击杀次数限制- 皇宫乱斗
         */
        int BRAWLING_RELIVE_BUFF_DEAD_COUNT = 100561;
        /**
         * buff刷新规则- 皇宫乱斗
         */
        int BRAWLING_BUFF_RULE = 100563;
        /**
         * 阵营人数差规则- 皇宫乱斗
         */
        int BRAWLING_GROUP_RULE = 100564;
        /**
         * 击杀bossbuff - 皇宫乱斗
         */
        int BRAWLING_BOSS_BUFF = 100565;
        /**
         * 场景buff刷新时间 - 皇宫乱斗
         */
        int BRAWLING_BUFF_REFRESH_TIME = 100566;
        /**
         * 离开场景所需删除的buff - 皇宫乱斗
         */
        int BRAWLING_REMOVE_BUFF_LIST = 100568;

        /**
         * 交易系统开启验证
         */
        int TRADE_CONDITION = 100125;

        /**
         * 竞技场离队惩罚
         */
        int ARENA_LEAVE_TEAM = 100580;

        /**
         * 竞技场击杀buff转移百分数
         */
        int ARENA_BUFF_COUNT = 100581;

        /**
         * 竞技场积分百分比
         */
        int ARENA_SCORE_PENCENT = 100582;

        /**
         * 竞技场奖励配置
         */
        int ARENA_REWARD_INFO = 100584;

        /**
         * 祭坛：Boss协助奖励的box的id
         */
        int JI_TAN_BOSS_HELP_REWARD = 100592;

        /**
         * 祭坛：可获得圣火的boss的id
         */
        int JI_TAN_BOSS = 100593;

        /**
         * 圣祭坛的bossid
         */
        int JI_TAN_SHENG = 100594;

        /**
         * 祭坛首领奖励次数#首领协助次数#宝箱采集次数
         */
        int JI_TAN_COUNT = 100595;

        /**
         * 祭坛Boss奖励次数可购买上限#消耗货币#单次价格
         */
        int JI_TAN_BUY = 100596;

        /**
         * 祭坛：圣祭坛激活所需圣火
         */
        int JI_TAN_NEED_VALUE = 100598;

        /**
         * 祭坛：地图id，按顺序东南西北圣
         */
        int JI_TAN_MAP_ID = 100599;

        /**
         * 祭坛：圣地图关闭时间
         */
        int JI_TAN_CLOSE_MAP_CD = 100600;

        /**
         * 天下第一buff
         */
        int WORLD_FIRST_BUFF_ITEM = 100610;

        /**
         * 天下第一军饷奖励CD
         */
        int WORLD_FIRST_REWARD_CD = 100611;

        /**
         * 天下第一buff刷新CD
         */
        int WORLD_FIRST_BUFF_CD = 100612;

        /**
         * 天下第一最后一层锁死倒计时
         */
        int WORLD_FIRST_LOCK_TIME = 100613;

        /**
         * 天下第一冠军奖励
         */
        int WORLD_FIRST_KEMP_REWARD = 100614;

        /**
         * 天下第一冠军时装
         */
        int WORLD_FIRST_KEMP_FASHION = 100630;

        /**
         * 实名认证状态道具
         */
        int AUTHENTICATION_ITEM = 100706;

        /**
         * 关注微信公众号道具
         */
        int WECHAT_ITEM = 100705;

        /**
         * 英雄转职需要删除的装备部位
         */
        int HERO_CAREER_CHANGE_UPDATE_EQUIP_POS = 100640;

        /**
         * 英雄转职需要卸下的装备部位
         */
        int HERO_CAREER_CHANGE_CHECK_EQUIP_POS = 100641;

        /**
         * 刷新任务地图编号
         */
        int REFRESH_TASK_MAPS = 100801;

        /**
         * 竞技场积分buff
         */
        int ARENA_SCORE_BUFF = 100586;

        /**
         * 火龙之心快速充能次数
         */
        int FAST_BUY_HUO_LONG_VALUE_COUNT = 100822;

        /**
         * 神秘商店自动刷新间隔
         */
        int MYSTERY_SHOP_REFRESH_CD = 100901;
        /**
         * 神秘商店免费刷新次数
         */
        int MYSTERY_SHOP_REFRESH_COUNT = 100902;
        /**
         * 神秘商店手动刷新消耗
         */
        int MYSTERY_SHOP_REFRESH_COST = 100903;
        /**
         * 周期累充循环周期
         */
        int CYCLE_TOTAL_RECHARGE_STAGE = 100832;
        /**
         * 周期累充最大周期数
         */
        int TOTAL_RECHARGE_MAX_STAGE = 100834;
        /**
         * 周期累充一周期天数
         */
        int TOTAL_RECHARGE_STAGE_TIME = 100836;
        /**
         * 怪物额外掉落参数
         */
        int MONSTER_DROPUP_PARAM = 101001;

        /**
         * 补签条件
         */
        int SIGNED_CONDITION = 101007;

        /**
         * 免费补签次数
         */
        int FREE_SIGNED_COUNT = 101009;

        /**
         * 付费补签消耗
         */
        int SIGNED_COST = 101010;
        /**
         * 攻速比例配置
         * 影响技能公共CD
         */
        int SKILL_CD_REDUCE_RATIO = 103004;

        /**
         * 连杀判定时间
         */
        int COMBO_TIME = 109101;

        /**
         * 掉落公告稀有度(系统频道)
         */
        int ANNOUNCE_DROP_RARE_SYSTEM = 109601;

        /**
         * 掉落公告稀有度(普通公告)
         */
        int ANNOUNCE_DROP_RARE_NORMAL = 109602;
        /**
         * 最低攻击伤害
         */
        int MIN_ATTACK = 109901;
        /**
         * 重置天赋消耗
         */
        int TALENT_CLEAR = 113001;


        /**
         * 神秘商店刷新时间
         */
        int MYSTERY_STORE_FLUSH_TIME = 117001;
        /**
         * 神秘商店刷新消耗
         */
        int MYSTERY_STORE_FLUSH_COST = 117002;
        /**
         * 专属面板通知稀有度限制
         */
        int ZHUANSHU_PANEL_RARE_LIMIT = 1300200;

        /**
         * 治疗消耗物
         */
        int CURE_COST = 102002;
        /**
         * 积分商店刷新消耗
         */
        int JI_FEN_STORE_REFRESH_COST = 180001;

        /**
         * 积分商店免费刷新间隔
         */
        int JI_FEN_STORE_AUTO_REFRESH_GAP = 180002;

        /**
         * 积分商店每日免费刷新次数
         */
        int JI_FEN_STORE_FREE_REFRESH_COUNT = 180003;

        /**
         * 每次刷新商品数量
         */
        int JI_FEN_STORE_REFRESH_GOOD_COUNT = 180004;


        /**
         * 退出副本获取装备
         */
        int DUPLICATE_EXIT_EQUIP = 101011;


        /**
         * 怪物清除仇恨时间
         */
        int MONSTER_CLEAR_THREAD_TIME = 101013;

        /**
         * 邀请码开启天数
         */
        int INVITATION_CODE_EFFECTIVE_DAY = 160101;

        /**
         * 服务器可注册人数上限
         */
        int INVITATION_CODE_SERVER_REGISTER_MAX = 160102;

        /**
         * 老玩家可生成邀请码的条件
         */
        int INVITATION_CODE_GENERATE_CONDITION = 160103;

        /**
         * 每条邀请码可使用上限
         */
        int INVITATION_CODE_USE_LIMIT_MAX = 160104;

        /**
         * 挖宝装备出处名称
         */
        int DIT_TREASURE_MAP = 160005;

        /**
         * 创角称号自动消失条件等级
         */
        int CREATE_DEFAULT_FASHION_VANISH = 330000;

        /**
         * 神威BOSS每日击杀boss掉落奖励次数
         */
        int SHEN_WEI_BOSS_DROP_COUNT_LIMIT = 340000;

        /**
         * 皇榜挑战参与次数限制
         *
         * @deprecated 策划不用了
         */
        @Deprecated
        int EDICT_CHALLENGE_AID_COUNT_LIMIT = 370002;

        int MOVE_NETWORK_PARAM = 400001;
        /**
         * 通天之路排行榜第一公告
         */
        int ANNOUNCE_BABEL_ROAD_FIRST = 410001;

        /**
         * 暗黑神殿每日0点后累加时间
         * itemConfigId#count
         */
        int DAILY_DARK_TEMPLE = 430002;

        /**
         * 暗黑神殿累加时间condition
         */
        int CONDITION_DARK_TEMPLE = 430003;
        /**
         * 相同法宝列表
         */
        int MAGIC_WEAPON_SAME = 440005;

        /**
         * 新号送一次暗黑神殿时间
         * 开服时间#itemConfigId#count
         */
        int FIRST_DARK_TEMPLE_REWARD = 460001;

        /**
         * 每日免费领取体力值
         */
        int PHYSICAL_POWER_RECEIVE = 540103;

        /**
         * 持续移动判定下限
         */
        int PERSIST_MOVE_LIMIT_LOWER = 570001;

        /**
         * 持续移动判定上限
         */
        int PERSIST_MOVE_LIMIT_UPPER = 570002;

        /**
         * 足迹充能完成公告
         */
        int FOOTPRINT_ANNOUNCE = 570003;

        /**
         * 阵法升级暴击有效类型
         */
        int MAGIC_CIRCLE_UP_CRIT_VALID_TYPE = 571001;

        /**
         * 会长未上线自动弹劾时间(天)
         */
        int IMPEACH_DAY = 601001;

        /**
         * 会长自动弹劾全行会邮件id
         */
        int IMPEACH_MAIL = 601002;


        /**
         * 行会掉落助攻邮件
         */
        int UNION_ASSIST_DROP_MAIL = 580025;

        /**
         * 护盾值回复cd
         */
        int SHIELD_REPLY_CD = 606005;

        /**
         * 杀同一玩家多次后不在加积分
         */
        int SHA_BA_KE_KILL_NUM = 580026;

        /**
         * 天魔来袭要同步的怪物id
         */
        int TIAN_MO_SYN_MONSTER = 608002;

        /**
         * 九天之巅死亡掉落层数
         */
        int JIU_TIAN_DIAO_LUO_CENG_SHU = 610001;

        /**
         * 九天之巅击杀玩家积分
         */
        int JIU_TIAN_KILL_PLAYER_JI_FEN = 610002;

        /**
         * 九天之巅击杀怪物积分
         */
        int JIU_TIAN_KILL_MONSTER_JI_FEN = 610003;

        /**
         * 九天之巅需要给前端发送怪物刷新倒计时的地图
         */
        int JIU_TIAN_NEED_RECORD_MAP = 610005;

        /**
         * 缴械装备部位列表
         */
        int DISARM_POS = 619001;

        /**
         * 缴械属性
         */
        int DISARM_ATTRIBUTE = 619002;

        /**
         * 旗子存在玩家身上时间（秒）
         */
        int QI_ZI_EXIST_TIME = 620004;

        /**
         * 旗子特效
         */
        int QI_ZI_TE_XIAO = 620009;

        /**
         * 武帝城禁地地图id
         */
        int WU_DI_CHENG_JIN_DI_MAPS = 580029;

        /**
         * 宠物装备部位
         */
        int CHONGWU_EQUIP_POS = 622002;

        /**
         * 宠物装备强化系统开启条件condition
         */
        int CHONGWU_EQUIP_CONDITION = 622003;

        /**
         * 宠物装备鉴定获得技能万分比
         */
        int CHONGWU_EQUIP_JIANDING_SKILL = 622001;

        /**
         * 宠物装备鉴定随机品质权重
         */
        int CHONGWU_EQUIP_RATE_PROB = 622004;

        /**
         * 宠物装备点化消耗
         */
        int CHONGWU_EQUIP_DIANHUA_COST = 622005;

        /**
         * 商店购买商品上限
         */
        int STORE_BUYGOODS_MAXCOUNT = 640001;

        /**
         * 需要掉线重连的副本
         */
        int DUPLICATE_RECONNECT = 620010;

        /**
         * 3v3上榜条件
         */
        int XIU_LUO_RANK_LIMIT = 620011;

        /**
         * 3v3排行榜条数
         */
        int XIU_LUO_RANK_NUM = 620012;
        /**
         * 两个特殊装备的装备位
         */
        int SP_EQUIP_INDEX = 650001;

        /**
         * 特殊装备无限回城装备itemId
         */
        int SP_EQUIP_HUICHENG = 670001;

        /**
         * 特殊装备无限随机装备itemId
         */
        int SP_EQUIP_SUIJI = 670002;

        /**
         * 特殊装备无限大陆传送石装备itemId
         */
        int SP_EQUIP_DALU_CHUANSONG = 670004;

        /**
         * 特殊装备每日免费20次行会召集令装备itemId
         */
        int SP_EQUIP_DALU_UNION_ZHAOJI = 670003;

        /**
         * 刀刀元宝获取的道具id
         */
        int GOLD_ITEM_CONFIG_ID = 680001;

        /**
         * 人皇祭祀召唤怪物配置id
         */
        int SACRIFICE_MONSTER = 660004;

        /**
         * 神皇之力系统开启条件
         */
        int UNION_SHENHUANG_CONDITION = 700001;

        /**
         * 神皇之力次数限制
         */
        int UNION_SHENHUANG_COUNT = 700002;
        /**
         * 刷新任务地图不刷新的任务id
         */
        int REFRESH_TASK_MAPS_NOCHECK_TASK = 655001;

        /**
         * 星官怪物id
         */
        int XINGGUAN_MONSTERS_LIST = 721001;

        /**
         * 地狱之路-泡点获取积分间隔(s)
         */
        int HELL_ROAD_AUTO_SCORE_INTERVAL = 720001;

        /**
         * 地狱之路-泡点获取积分
         */
        int HELL_ROAD_AUTO_SCORE = 720002;

        /**
         * 地狱之路-击杀玩家基础积分
         */
        int HELL_ROAD_KILL_PLAYER_BASE_SCORE = 720003;

        /**
         * 地狱之路-击杀玩家积分获取百分比
         */
        int HELL_ROAD_KILL_PLAYER_SCORE_RATE = 720004;

        /**
         * 地狱之路-采集上限
         */
        int HELL_ROAD_COLLECT_LIMIT = 720005;

        /**
         * 地狱之路-活动地图id#形象id#武器id
         */
        int HELL_ROAD_PARAMS = 720006;

        /**
         * 地狱之路-出生随机点（x#y#范围）
         */
        int HELL_ROAD_INIT_POINT = 720007;

        /**
         * 地狱之路-击杀怪物积分
         */
        int HELL_ROAD_MONSTER_SCORE_MAP = 720008;

        /**
         * 地狱之路-活动地图
         */
        int HELL_ROAD_MAPS = 720009;

        /**
         * 地狱之路-离开地图积分降低比例
         */
        int HELL_ROAD_LEAVE_MAP_SCORE_RATE = 720010;

        /**
         * 地狱之路-排行榜人数限制
         */
        int HELL_ROAD_RANK_LIMIT = 720012;

        /**
         * 地狱之路-排行榜刷新间隔(ms)
         */
        int HELL_ROAD_RANK_REFRESH_INTERVAL = 720013;

        /**
         * 坐骑赞助点加点
         */
        int ZUOQI_ZANZHU_SCORE = 722002;

        /**
         * 坐骑赞助点击杀怪物列表
         */
        int ZUOQI_ZANZHU_MONSTER_LIST = 722003;

        /**
         * 搬砖任务和刺探任务
         */
        int BANZHUAN_CITAN_TASKLIST = 710014;

        /**
         * 铸魂-加锁后消耗
         */
        int ZHU_HUN_LOCK_COST = 724002;

        /**
         * 巅峰联赛通过沙巴克获取参赛资格时间
         */
        int DFLS_SBK_QUALIFICATIONS_DAY = 750006;

        /**
         * 巅峰联赛竞猜券道具id
         */
        int DFLS_JING_CAI_QUAN_ITEM_ID = 750047;

        /**
         * 需要修复的沙巴克限时时装
         */
        int FIX_CROSS_SBK_FASHIONID = 580032;

        /**
         * 灵宝怪物类型
         */
        int REALM_MONSTER_TYPE = 761002;

        /**
         * 灵宝怪物每日掉落次数
         */
        int REALM_DROP_LIMIT = 761003;

        /**
         * 灵宝怪物每日可购买掉落次数
         */
        int REALM_BUY_LIMIT = 761004;

        /**
         * 灵宝怪物每日掉落次数购买消耗
         */
        int REALM_BUY_COST = 761005;

        /**
         * 真言塔每日归属次数
         */
        int ZHENYAN_TOWER_DAILY_COUNT = 754001;

        /**
         * 真言塔特殊消耗
         */
        int ZHENYAN_TOWER_SP_COST = 754002;

        /**
         * 真言塔地图id
         */
        int ZHENYAN_TOWER_MAPID = 754003;

        /**
         * 离线挂机需要加倍的道具
         */
        int LIXIANGUAJI_MUTIL_ITEMID = 842007;

        int ARENA_BATTLE_COST = 940001;
        int ARENA_REFRESH_COST = 940002;
        int ARENA_DAY_SETTLE_TIME = 940003;
        int ARENA_WEEK_SETTLE_TIME = 940004;
        int ARENA_UNLOCK_CONDITION = 940005;
        int ARENA_BATTLE_REWARD = 940006;
        int ARENA_DAY_OPEN_TIME = 940008;

        /**
         * 竞技场结算时间#结算最小间隔
         */
        int ARENA_RESET_TIME = 940009;

        int YAO_XIE_OPEN_TIME = 960001;

        int YAO_XIE_END_TIME = 960002;

        int UNION_LEADER_VX = 960003;
        /**
         * 行会默认宣言
         */
        int UNION_DEFAULT_XUAN_YAN = 960004;

        int YAO_XIE_ATTR = 960005;

        int YAO_XIE_OP_DAY = 960006;

        int YAO_XIE_BOX = 960007;

        int YAO_XIE_ATTR_DURATION = 960008;

        int ZHEN_BAO_GE_OPEN_TIME = 960009;

        int ZHEN_BAO_GE_END_TIME = 960010;

        int XIE_ZHU_TIME = 960012;

        int XIE_ZHU_LIMIT = 960013;

        /**
         * 退出行会消耗
         */
        int QUIT_UNION_REMOVE_ITEM = 960014;
        /**
         * 修改行会名消耗
         */
        int CHANGE_UNION_NAME_COST = 960016;

        /**
         * 运势抽奖道具
         */
        int YUN_SHI_CHOU_JIANG_ITEM = 1000001;

        int HOARD_COST = 1001001;

        int HOARD_EXP = 1001002;

        int SHENMO_LILIAN_REFRESH = 920002;

        int ZHEN_YAO_TA_RANK_NUM = 1003001;

        int ARENA_EXTRA_REWARD = 1004002;

        int BOSS_KING_EXTRA_REWARD = 1004001;
        /**
         * 战令额外奖励 格式：活动id#购买次数#奖励boxId
         */
        int ZHAN_LING_EXTRA_REWARD = 2000002;

        /**
         * 周刊数迴单次活动奖励轮次上限
         */
        int ZHOU_KAN_WHEEL = 2000003;

        /**
         * 周刊数迴未领取奖励补发邮件id
         */
        int ZHOU_KAN_TASK_MAIL = 2000004;

        /**
         * 神魔历练次数回复时间
         */
        int SHENMO_LILIAN_COUNT_TIME_GAP = 920003;

        /**
         * 战令礼包额外奖励补发邮件id
         */
        int ZHAN_LING_PACK_EXTRA_MAIL = 2000006;


        int ENERGY_DRINK_RECHARGE = 1005001;


        /**
         *  每日改名次数
         *  次数
         */
        int GAI_MING = 2000028;

        /**
         *  改名消耗道具
         *  道具ID#数量
         */
        int GAI_MING_ITEM = 2000029;

        int NAME_LENGTH = 2000030;

        /**
         * 五行山挑战次数上限
         */
        int WU_XING_SHAN_LIMIT = 2000031;

        /**
         * 五行山挑战时间上限
         */
        int WU_XING_SHAN_END_TIME = 2000033;

        /**
         * 获取奖励需要拆分的活动type
         */
        int CHAI_FEN_JIANG_LI_TYPE = 2000032;

        /**
         * 仙盟宝箱普通宝箱列表上限
         */
        int UNION_CHEXT_NORMAL_LIMIT = 960020;

        /**
         * 仙盟宝箱稀有宝箱列表上限
         */
        int UNION_CHEXT_RARE_LIMIT = 960021;

        /**
         * 珍宝阁最低价后强制砍价区间
         */
        int ZHEN_BAO_GE_YI_JIA_AFTER_MIN_PRICE = 960022;


        /**
         * 珍宝阁砍价比例参数
         */
        int ZHEN_BAO_GE_KAN_JIA_PARAM = 960025;

        /**
         * 珍宝阁砍价人数比例
         */
        int ZHEN_BAO_GE_KAN_JIA_MEMBER_COUNT_RATE = 960026;

        /**
         * 暴击增伤倍率
         */
        int BAO_JI_ZENG_SHANG_BEI_LV = 2000016;

        int MAIL_AUTO_DELETE_DAY = 100007;

        /**
         *  单场战斗最大轮次
         */
        int BATTLE_MAX_ROUND = 2000019;

        /**
         * 默认聊天气泡
         * fashionId
         */
        int CREATE_DEFAULT_CHAT_BUBBLE = 1080002;

        /**
         * 联盟宝箱钥匙道具ID#经验道具ID
         */
        int YAO_SHI_JING_YAN_ITEM = 961003;

        /**
         * 公共 cd
         */
        int ZT_COMMON_CD = 10001;

        /**
         * 离线奖励离线时长上限, 单位分钟
         */
        int LI_XIAN_TIME_LIMIT = 11201;

        /**
         * 自选抽卡达到x次后增加自选概率
         */
        int OPTIONAL_DRAW_NEED_COUNT = 11500;

        /**
         * 自选每次抽卡增加概率
         */
        int OPTIONAL_DRAW_INCREASE_PROBABILITY = 11501;

        /**
         * 自选抽卡消耗
         */
        int OPTIONAL_DRAW_COST = 11502;

        /**
         * 自选抽卡全服记录最大值
         */
        int OPTIONAL_DRAW_SERVICE_MAX_COUNT = 11503;

        int STONE_PUT_ON_COST = 11600;

        /**
         * 对应的装备品质，能够开启锻造
         */
        int DUAN_ZAO_QUALITY = 11800;

        /**
         * 服务内可创建的最大角色数量（小于等于），超出后不可创建
         */
        int MAX_CREATE_ROLE_LIMIT = 2000042;

        /**
         * 伙伴 不同品质提升的暴击增幅
         */
        int HUO_BAN_QUALITY_CRITICAL_HIT = 200301;

        /**
         * 伙伴 初始暴击增幅
         */
        int HUO_BAN_CRITICAL_HIT_INIT_VALUE = 200302;

        /**
         * 伙伴 编队限制信息
         */
        int HUO_BAN_FORMATION_LIMIT = 200303;

        /**
         * 伙伴 初始编队英雄
         */
        int HUO_BAN_FORMATION_ID = 200304;

        /**
         * 伙伴 全职业经验的itemid
         */
        int HUO_BAN_ALL_EXP_ITEM_ID = 200306;
    }
}
