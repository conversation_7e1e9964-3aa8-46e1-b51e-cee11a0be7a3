package com.sh.game.common.communication.notice;

import com.sh.game.notice.Notice;
import com.sh.game.notice.ProcessNotice;
import lombok.Getter;

/**
 * <AUTHOR>
 * @date 2018/6/13 15:11
 */
@Getter
@Notice
public class EquipUpdateNotice extends ProcessNotice {

    private long rid;

    private long actorId;

    private int pos;

    private int itemId;

    private long lid;

    public EquipUpdateNotice() {

    }

    public EquipUpdateNotice(long rid, long actorId, int pos, int itemId, long lid) {
        this.rid = rid;
        this.actorId = actorId;
        this.pos = pos;
        this.itemId = itemId;
        this.lid = lid;
    }

}
