package com.sh.game.common.communication.msg.system.tilishitang;

import com.sh.game.common.msg.RPC;
import com.sh.game.msg.AbsProtostuffMessage;
import java.lang.Override;

/**
 * 请求体力食堂领取体力
 * 该文件由工具根据 tilishitang.xml 文件自动生成，不可修改
 */
@RPC("toLogic")
public class ReqTiLiShiTangReceive extends AbsProtostuffMessage {
  /**
   * 领取的体力id 小于等于0领取全部
   */
  private int tiLiId;

  @Override
  public int getId() {
    return 505004;
  }

  public void setTiLiId(int tiLiId) {
    this.tiLiId = tiLiId;
  }

  public int getTiLiId() {
    return this.tiLiId;
  }
}
