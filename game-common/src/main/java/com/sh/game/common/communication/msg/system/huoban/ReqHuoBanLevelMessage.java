package com.sh.game.common.communication.msg.system.huoban;

import com.sh.game.common.msg.RPC;
import com.sh.game.msg.AbsProtostuffMessage;
import java.lang.Override;

/**
 * 请求升级伙伴
 * 该文件由工具自动生成，不可修改
 */
@RPC("toLogic")
public class ReqHuoBanLevelMessage extends AbsProtostuffMessage {
  /**
   *  伙伴的配置id
   */
  private int configId;

  @Override
  public int getId() {
    return 388003;
  }

  public void setConfigId(int configId) {
    this.configId = configId;
  }

  public int getConfigId() {
    return this.configId;
  }
}
