package com.sh.game.common.communication.msg.system.tilishitang;

import com.sh.game.common.communication.msg.system.tilishitang.bean.PBTiLiBean;
import com.sh.game.common.msg.RPC;
import com.sh.game.msg.AbsProtostuffMessage;
import java.lang.Override;
import java.util.ArrayList;
import java.util.List;

/**
 * 返回体力食堂全部数据信息
 * 该文件由工具根据 tilishitang.xml 文件自动生成，不可修改
 */
@RPC("toClient")
public class ResTiLiShiTangInfo extends AbsProtostuffMessage {
  /**
   * 待领取的体力列表
   */
  private List<PBTiLiBean> tiLiBean = new ArrayList<>();

  /**
   * 下一次体力恢复毫秒值
   */
  private long nextTiLiRecoverMill;

  /**
   * 下一次体力恢复数量
   */
  private int nextTiLiRecoverNum;

  @Override
  public int getId() {
    return 505002;
  }

  public void setTiLiBean(List<PBTiLiBean> tiLiBean) {
    this.tiLiBean = tiLiBean;
  }

  public List<PBTiLiBean> getTiLiBean() {
    return this.tiLiBean;
  }

  public void setNextTiLiRecoverMill(long nextTiLiRecoverMill) {
    this.nextTiLiRecoverMill = nextTiLiRecoverMill;
  }

  public long getNextTiLiRecoverMill() {
    return this.nextTiLiRecoverMill;
  }

  public void setNextTiLiRecoverNum(int nextTiLiRecoverNum) {
    this.nextTiLiRecoverNum = nextTiLiRecoverNum;
  }

  public int getNextTiLiRecoverNum() {
    return this.nextTiLiRecoverNum;
  }
}
