# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## Project Overview

MaoXianWorld is a Java-based multiplayer online game server project built with Maven. It's a modular game server architecture supporting features like player management, combat systems, guilds, activities, and cross-server functionality.

## Build and Development Commands

### Initial Setup
1. Run `conf/updateCsv.bat` to initialize CSV configuration files from SVN repository
2. Create local database named 'maoxianworld'
3. Use IntelliJ IDEA run configuration `.run/GameBootStrap.run.xml` to start the game server

### Maven Commands
- `mvn clean compile` - Clean and compile all modules
- `mvn test` - Run JUnit 5 tests (configured for parallel execution)
- `mvn test -Dtest=ClassName#methodName` - Run a single test method
- `mvn test -Dtest=ClassName` - Run all tests in a specific class
- `mvn test -pl game-common` - Run tests for a specific module
- `mvn package` - Package all modules into JARs

### Testing
- Tests use JUnit 5 Jupiter
- Test files follow pattern: `**/*Test.java`, `**/*Tests.java`, `**/*TestCase.java`
- Parallel test execution enabled with 2 threads

## Module Architecture

The project follows a multi-module Maven structure:

### Core Modules
- **game-server**: Main game logic, player systems, handlers
- **game-common**: Shared utilities, constants, configuration management
- **game-basic**: Core game abstractions and networking foundations
- **game-scene**: Map system, AI, combat mechanics, buff system
- **game-script**: Groovy scripting system for game logic

### Service Modules  
- **game-gate**: Entry point bootstrap (GameBootstrap main class)
- **game-merge**: Server merge utilities and database operations
- **game-back-client**: Administrative/backend client tools

### Packaging
- **game-package**: Contains packaging configurations for different server types

### Configuration System
- CSV-based configuration loaded from `data/` directory
- Properties files in `conf/` for different environments
- Configuration validation via ConfigLoadChecker

## Key Architecture Patterns

### Message System
- **Message Pool Architecture**: AbstractMessagePool base class with AllMessagePool auto-generated routing
- **Handler Registration**: Handlers extend MessageHandler<T> with @HandlerProcessor for queue assignment
- **Serialization**: Protostuff-based serialization with SerializationUtil utilities
- **Auto-registration**: @RPC annotations enable automatic service registration via AutoRegisterRpcService

### Module Communication
- **Notice System**: NoticeCenter manages cross-module notifications with timeout handling
- **RPC Communication**: ModuleClient and RPCConnection for inter-module calls
- **Event Architecture**: EventUtil central dispatcher with IListener<T> typed listeners
- **Async Patterns**: ProcessNotice and TimeProcessNotice for non-blocking communication

### Data Persistence & Synchronization
- **@Sync Annotation System**: Automatic server-scene data synchronization for player/game state
- **Persist Layer**: PersistDao abstract base with @PersistName/@PersistPrefix/@PersistPeriod annotations
- **Config Cache System**: IConfigCache with @ConfigCache auto-registration for CSV-based configurations
- **Database Operations**: Custom persistence layer with batch operations and complex object serialization

### Scene/Map System
- **Map Object Hierarchy**: PlayerActor/MonsterActor extend base map objects in GameMap containers
- **AOI System**: TowerAOI provides efficient Area of Interest calculations for visibility
- **Combat System**: BuffManager for effects, SkillFactory/SkillEffectFactory for abilities
- **Object Lifecycle**: Enter/exit events manage map object states

### Processing & Threading
- **Processor Queue System**: ProcessorId constants with @HandlerProcessor for load balancing
- **Queue Separation**: Different queues for player actions, common operations, system tasks
- **Thread Safety**: Immutable collections and careful synchronization patterns throughout

### Configuration Management
- **CSV-Based Configuration**: Data loaded from data/ directory into strongly-typed config objects
- **Hot-Reloading**: ConfigLoadChecker validates and supports runtime config updates
- **Caching Strategy**: Immutable config caches built at startup for performance

### Scripting Integration
- **Groovy Event Hooks**: Comprehensive event system with IEvent* interfaces for game actions
- **Script Development Mode**: Hot-reloading enabled via -Dgame.script.dev=true
- **Event Types**: Extensive event hooks for player lifecycle, combat, social interactions

## Development Environment

### Java Configuration
- Java 8 target/source compatibility
- Uses base framework version 4.2.8-SNAPSHOT
- Key dependencies: Netty 4.1.94, MySQL 8.0.21, Log4j 2.15.0

### Development Features
- Script development mode enabled via `-Dgame.script.dev=true`
- Extensive logging and monitoring capabilities
- Memory settings: 3GB heap, G1GC collector
- Main entry point: `com.sh.game.gate.GameBootstrap`
- VM parameters include heap dump on OOM and verbose GC logging

## Important Notes

### Recently Removed Modules
A significant number of activity and feature modules were recently deleted (see `已删除模块.md`). When working on activity-related features, check if the module still exists before referencing it.

Key deleted systems include: pet modules, building/construction modules, various activity modules (equipment collection, boss feast, investment activities), weapon modules, auction system, and many seasonal/event-specific features.

### Network Architecture
- WebSocket support enabled
- Rate limiting: 50 messages per second per client
- High/low water mark buffering for network performance

### Testing and Quality
- SLF4J + Log4j2 logging framework
- Built-in message monitoring and validation
- Sensitive word filtering system integrated

### Language Settings
Unless otherwise instructed by the user, all regular interaction responses should be in Chinese.

# important-instruction-reminders
Do what has been asked; nothing more, nothing less.
NEVER create files unless they're absolutely necessary for achieving your goal.
ALWAYS prefer editing an existing file to creating a new one.
NEVER proactively create documentation files (*.md) or README files. Only create documentation files if explicitly requested by the User. 
