package com.sh.game.map.duplicate.instance.ztxy;

import com.sh.common.config.ConfigDataManager;
import com.sh.game.common.communication.msg.map.duplicate.ResDuplicateEndMessage;
import com.sh.game.common.communication.msg.map.duplicate.ResStageUpdateMessage;
import com.sh.game.common.communication.notice.BarrierDuplicateParam;
import com.sh.game.common.communication.notice.CompleteDuplicateNotice;
import com.sh.game.common.config.model.DuplicateConfig;
import com.sh.game.common.config.model.MapConfig;
import com.sh.game.common.config.model.MapLevelConfig;
import com.sh.game.common.config.model.MonsterConfig;
import com.sh.game.common.constant.AttributeConst;
import com.sh.game.common.constant.MapConst;
import com.sh.game.common.constant.MonsterType;
import com.sh.game.common.constant.ProcessorId;
import com.sh.game.common.entity.attr.Attribute;
import com.sh.game.common.entity.buff.BuffConst;
import com.sh.game.common.entity.buff.BuffState;
import com.sh.game.common.entity.skill.Skill;
import com.sh.game.common.util.RandomUtil;
import com.sh.game.common.util.TimeUtil;
import com.sh.game.map.Module;
import com.sh.game.map.aoi.AOIEventListenerImpl;
import com.sh.game.map.aoi.AOIEventTeam;
import com.sh.game.map.aoi.TowerAOI;
import com.sh.game.map.buff.BuffTriggerUtil;
import com.sh.game.map.constant.Dir;
import com.sh.game.map.duplicate.Duplicate;
import com.sh.game.map.duplicate.condition.DuplicateEndCondition;
import com.sh.game.map.obj.MapObjectFactory;
import com.sh.game.map.obj.MonsterActor;
import com.sh.game.map.obj.PlayerActor;
import com.sh.game.map.scene.point.Point;
import com.sh.game.map.scene.spawn.Spawn;
import com.sh.game.map.scene.topography.Topography;
import lombok.Getter;
import lombok.extern.slf4j.Slf4j;

import java.util.Collections;
import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * 择天西游的关卡副本
 */
@Slf4j
public class ZtxyBarrierDuplicate extends Duplicate {
    /**
     * 关卡编号
     */
    @Getter
    private int barrierNo = 0;
    /**
     * 困难等级
     */
    @Getter
    private int difficulty = 1;
    /**
     * 当前阶段
     */
    private Stage stage = Stage.KILL;

    public int globalSkillCount;

    private enum Stage {
        /**
         * 清怪阶段
         */
        KILL,
        /**
         * boss阶段
         */
        BOSS,
    }

    /**
     * 能量，能量满了之后进入下一个阶段
     */
    private int energy;
    /**
     * 当前阶段的结束时间
     */
    private long stageEndTime;

    private DuplicateConfig duplicateConfig;
    @Getter
    private MapLevelConfig mapLevelConfig;
    @Getter
    private int bossConfigId;
    @Getter
    private int freeReliveCount;
    @Getter
    private int reliveCount;

    public void decReliveCount() {
        if (reliveCount > 0) {
            reliveCount--;
        }
    }

    public void decFreeReliveCount() {
        if (freeReliveCount > 0) {
            freeReliveCount--;
        }
    }

    public boolean isUnLimedTime() {
        return barrierNo == 0;
    }

    @Override
    public boolean initDuplicate() {
        // 根据百分比给一批怪物携带全局技能
        for (int[] params : mapLevelConfig.getGlobalSkill()) {
            int skillId = params[0];
            double percent = params[1] / 100.0;
            List<MonsterActor> monsters = monsterMap.values().stream().filter(monster -> monster.getMonsterType() == 1).collect(Collectors.toList());
            int count = (int) (monsters.size() * percent);
            List<MonsterActor> randoms = RandomUtil.randomElements(monsters, count);
            for (MonsterActor monster : randoms) {
                Skill skill = new Skill();
                skill.setSkillId(skillId);
                skill.setLevel(1);
                monster.addSkill(Collections.singletonList(skill));
                monster.getGlobalSkills().add(skillId);
                BuffTriggerUtil.triggerBuff(monster, monster, BuffConst.TriggerType.AFTER_ENTER_MAP, null, TimeUtil.getNowOfMills());
            }
        }
        return true;
    }

    @Override
    public void init(long key, int host, MapConfig config, Topography topography, Object param) {
        setId(key);
        remoteHostId = host;
        cfgId = config.getMapCode();
        name = config.getMapName();
        setNeedCostPerSec(config.isNeedCostPerSec());
        safe = config.isSafe();
        this.topography = topography;
        aoi = new TowerAOI(this.topography);
        aoi.addListener(AOIEventListenerImpl.getInstance());
        aoi.addListener(new AOIEventTeam());
        if (param != null) {
            difficulty = ((BarrierDuplicateParam) param).getDifficulty();
            barrierNo = ((BarrierDuplicateParam) param).getBarrierNo();
        }
        duplicateConfig = ConfigDataManager.getInstance().getById(DuplicateConfig.class, cfgId);
        this.state = MapConst.MAP_STATE.WAITING;
        if (duplicateConfig == null) {
            log.error("duplicate init failed, duplicate config not found:{}", cfgId);
            return;
        }
        MapLevelConfig mapLevelConfig = ConfigDataManager.getInstance().getById(MapLevelConfig.class, barrierNo);
        if (mapLevelConfig == null || mapLevelConfig.getBossId() == 0) {
            log.error("关卡没有配置boss！{}", duplicateConfig.getId());
            return;
        }
        this.mapLevelConfig = mapLevelConfig;
        freeReliveCount = mapLevelConfig.getFreeReborn();
        reliveCount = Math.max(0, mapLevelConfig.getRebornNum() - freeReliveCount);
        bossConfigId = mapLevelConfig.getBossId();
        this.setCreateType(duplicateConfig.getOpenType());
        this.setCreateTime(TimeUtil.getNowOfMills());
        this.setWaitingCloseTime(duplicateConfig.getWaitingCloseTime() * 1000);

        if (duplicateConfig.getTotalTime().length > 0) {
            this.setTotalTime(duplicateConfig.getTotalTime()[0]);
        }
        DuplicateEndCondition[][] successConditions = createCondition(duplicateConfig.getSucCondition());
        this.setSuccessConditions(successConditions);
        onInit();
        spawn = new Spawn(this);
        spawn.init();
        state = MapConst.MAP_STATE.INIT;
        globalSkillCount = 10;
        enterKillStage();
    }

    private void sendCurrentStageMsg() {
        ResStageUpdateMessage msg = new ResStageUpdateMessage();
        msg.setStage(stage.toString());
        msg.setEnergy(energy);
        msg.setDuplicateCfgId(duplicateConfig.getId());
        msg.setEndTime(isUnLimedTime() ? -1 : stageEndTime);
        Module.MSG_TRANSFORMER.sendMapMessage(msg, this);
    }

    private void enterKillStage() {
        stage = Stage.KILL;
        stageEndTime = TimeUtil.getNowOfMills() + mapLevelConfig.getKillTime() * 1000L;
    }

    private void enterBossStage() {
        stage = Stage.BOSS;
        // 清空地图上所有的怪物（召唤物.宠物除外）
        monsterMap.values().
                stream()
                .filter(actor -> actor.getMonsterType() != MonsterType.SKILL_CALL && actor.getMonsterType() != MonsterType.CHONG_WU)
                .forEach(actor -> {
                    actor.setDead(true);
                    actor.setHp(0);
                    actor.setKillerId(0);
                });
        stageEndTime = TimeUtil.getNowOfMills() + mapLevelConfig.getBossTime() * 1000L;
        MonsterActor boss = MapObjectFactory.createMonster(bossConfigId, null, false);
        PlayerActor playerActor = playerMap.values().stream().findFirst().orElse(null);
        if (boss != null && playerActor != null) {
            enterMonster(boss, playerActor.getPoint(), false);
        }
    }

    @Override
    public boolean checkEnd(int delta) {
        if (success) {
            return true;
        }
        if (stage == Stage.KILL && energy >= mapLevelConfig.getEnergyMax()) {
            enterBossStage();
            sendCurrentStageMsg();
        }
        if (!isUnLimedTime() && TimeUtil.getNowOfMills() > stageEndTime) {
            success = false;
            return true;
        }
        return false;
    }


    @Override
    public void afterPlayerEnter(PlayerActor player, Point point) {
        super.afterPlayerEnter(player, point);
        sendCurrentStageMsg();
    }


    @Override
    public void onMonsterDie(MonsterActor monster) {
        super.onMonsterDie(monster);
        MonsterConfig config = ConfigDataManager.getInstance().getById(MonsterConfig.class, monster.getConfigId());
        // 击杀小怪增加地图进度
        if (bossConfigId != config.getId()) {
            energy += config.getEnergy();
        } else if (monster.getConfigId() == bossConfigId) { // 击杀boss，关卡结束
            success = true;
            stageEndTime = 0;
        }
    }

    @Override
    public void onClose() {
        this.state = MapConst.MAP_STATE.CLOSING;
        ResDuplicateEndMessage res = new ResDuplicateEndMessage();
        res.setSucess(success ? 1 : 0);
        if (success) {
            res.setCompletionTime((int) ((TimeUtil.getNowOfMills() - createTime) / 1000L));
        }
        res.setWaitingTime(this.waitingCloseTime);
        res.setCfgId(cfgId);
        res.setFreeReliveCount(freeReliveCount);
        res.setReliveCount(reliveCount);
        Module.MSG_TRANSFORMER.sendMapMessage(res, this);
    }

    /**
     * 结算副本
     */
    private void sendCompleteDuplicateNotice() {
        for (PlayerActor player : playerMap.values()) {
            CompleteDuplicateNotice notice = new CompleteDuplicateNotice();
            notice.setRid(player.getRid());
            notice.setDuplicateID(cfgId);
            notice.setSuccess(isSuccess());
            notice.setBarrierNo(barrierNo);
            notice.setCompleteTime((int) ((TimeUtil.getNowOfMills() - createTime) / 1000));
            notice.addHost(player.getHostId());
            Module.NOTICE_TRANSFORMER.sendNotice(ProcessorId.SERVER_PLAYER, notice, player.getRid());
        }
    }


    @Override
    public void afterEnterMap(PlayerActor player) {
        player.setDir(Dir.RIGHT.getIndex());
        BuffTriggerUtil.triggerBuff(player, player, BuffConst.TriggerType.PASSIVE_SKILL, null, TimeUtil.getNowOfMills());
        super.afterEnterMap(player);
    }

    @Override
    public void beforePlayerExit(PlayerActor player) {
        // 结算副本
        sendCompleteDuplicateNotice();
        // 清理buff修改的属性
        Attribute buffAttr = player.getAttributeMap().get(AttributeConst.AttributeType.Cate.BUFF);
        if (buffAttr != null) {
            buffAttr.clear();
        }
        Attribute buffRatioAttr = player.getAttributeMap().get(AttributeConst.AttributeType.Cate.BUFF_RATIO);
        if (buffRatioAttr != null) {
            buffRatioAttr.clear();
        }
        // 清理身上的buff
        player.getBuffTriggerMap().clear();
        player.getBuffs().getBuffMap().clear();
        player.getBuffs().getGroundBuffs().clear();
        player.setBuffState(new BuffState());
        super.beforePlayerExit(player);
    }

    @Override
    public void afterPlayerExit(PlayerActor playerActor) {
        setState(MapConst.MAP_STATE.CLOSED);
        destroy();
    }

    @Override
    public void onPlayerDie(PlayerActor playerActor) {
        setSuccess(false);
        onClose();
    }

    @Override
    public void relive(PlayerActor playerActor) {
        this.state = MapConst.MAP_STATE.RUNNING;
        // 玩家复活重置关闭倒计时
        this.setWaitingCloseTime(duplicateConfig.getWaitingCloseTime() * 1000);
    }
}
