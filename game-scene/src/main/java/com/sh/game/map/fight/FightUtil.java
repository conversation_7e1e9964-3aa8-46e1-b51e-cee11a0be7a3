package com.sh.game.map.fight;

import com.sh.common.config.ConfigCacheManager;
import com.sh.common.config.ConfigDataManager;
import com.sh.game.common.config.cache.AttributeScoreCache;
import com.sh.game.common.config.cache.HuoBanConfigCache;
import com.sh.game.common.config.model.EquipSuitConfig;
import com.sh.game.common.config.model.MapLevelConfig;
import com.sh.game.common.config.model.MonsterConfig;
import com.sh.game.common.config.model.SkillEffectConfig;
import com.sh.game.common.constant.*;
import com.sh.game.common.entity.attr.Attribute;
import com.sh.game.common.entity.buff.BuffState;
import com.sh.game.common.util.RandomUtil;
import com.sh.game.map.Player;
import com.sh.game.map.duplicate.instance.ztxy.ZtxyBarrierDuplicate;
import com.sh.game.map.fight.entity.EffectContext;
import com.sh.game.map.fight.entity.FightResult;
import com.sh.game.map.obj.Performer;
import com.sh.game.map.obj.PlayerActor;
import com.sh.game.map.player.PlayerManager;
import com.sh.game.map.scene.GameMap;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.ArrayUtils;

import java.util.*;

@Slf4j
public class FightUtil {

    public static final double PERCENT = 10000D;

    /**
     * 将治疗具体到对象身上
     */
    public static long cure(long cure, Performer target) {
        long targetHp = target.getHp();
        long maxHp = AttributeEnum.ZTXY_HP.getAttrValue(target.getFinalAttribute());
        if (cure > maxHp - targetHp) {
            cure = maxHp - targetHp;
        }
        target.setHp(target.getHp() + cure);
        return cure;
    }

    public static long calBufferHurt(Performer caster, FightResult.BuffImmediateEffect buffEffect, int rate, int hurt) {
        long basicAttack = AttributeEnum.ZTXY_ATTACK.getAttrValue(caster.getFinalAttribute());
        double cfgRate = rate / PERCENT;
        return (long) Math.max(1, basicAttack * cfgRate + hurt);
    }

    public static long dealHurt(Performer caster, Performer target, EffectContext context, List<int[]> parameterList) {
        if (target.isDead() || target.getHp() <= 0) {
            return 0;
        }
        Attribute casterAttr = caster.getFinalAttribute();
        Attribute targetAttr = target.getFinalAttribute();
        SkillEffectConfig effectConfig = context.effectConfig;
        int[] parameter = parameterList.get(0);
        int hurtRate = parameter[0];
        int fixHurt = 0;
        if (parameter.length > 1) {
            fixHurt = parameter[1];
        }

        // 基础伤害
        float hurt = AttributeEnum.ZTXY_ATTACK.getAttrValue(casterAttr) - AttributeEnum.ZTXY_DEFENSE.getAttrValue(targetAttr);
        if (hurt <= 0) {
            hurt = 1;
        }
        hurt = Math.max(1f, hurt);

        // 冰冻、燃烧状态加成
        if (target.getBuffState().inStateOf(BuffState.State.ICICLED)) {
            hurt = hurt * (1 + AttributeEnum.ZTXY_FROST_DAMAGE_STRONG.getAttrValue(casterAttr) / 10000.0f);
        }
        if (target.getBuffState().inStateOf(BuffState.State.BURNING)) {
            hurt = hurt * (1 + AttributeEnum.ZTXY_FIRE_DAMAGE_STRONG.getAttrValue(casterAttr) / 10000.0f);
        }

        // 元素伤害
        AttributeEnum elementAttrEnum = AttributeEnum.valueOf(effectConfig.getElementAttrType() <= 0 ?
                -1 : effectConfig.getElementAttrType());
        if (elementAttrEnum != null) {
            float elementHurtPercent = findAttrPercent(casterAttr, targetAttr, elementAttrEnum);
            hurt *= elementHurtPercent;
            FightConst.FloaterType floaterType = FightConst.FloaterType.elementAttrType(elementAttrEnum.getType());
            context.replaceFloaterType(floaterType);
            // 累加元素印记
            context.elementMarking = target.incElementEffectLevel(elementAttrEnum);
        }
        // 范围伤害
        int targetType = context.effectConfig.getTargetType();
        if (targetType == FightConst.TargetType.SELF_RANGE || targetType == FightConst.TargetType.ENEMY_RANGE) {
            // 伤害 *（ 1 + AOE加成% - 敌人的AOE减免%）
            float positive = AttributeEnum.ZTXY_AOE_DAMAGE.getAttrValue(casterAttr) / 10000.0f;
            float negative = AttributeEnum.ZTXY_AOE_DAMAGE_REDUCTION.getAttrValue(targetAttr) / 10000.0f;
            hurt = hurt * (1 + positive - negative);
        }
        // 怪物类型加成（精英怪，boss怪）
        if (target.isMonster()) {
            float positive = 0;
            MonsterConfig monsterConfig = ConfigDataManager.getInstance().getById(MonsterConfig.class, target.getConfigId());
            if (monsterConfig.getBossJob() > 0) {
                positive = getAttrValueByBossJob(monsterConfig.getBossJob(), casterAttr, false);
            } else {
                if (monsterConfig.getType() == MonsterType.COMMON_BOSS) {
                    positive = AttributeEnum.ZTXY_ELITE_BOSS_DAMAGE.getAttrValue(casterAttr) / 10000.0f;
                }
                if (monsterConfig.getType() == MonsterType.JING_YING) {
                    positive = AttributeEnum.ZTXY_ELITE_MONSTER_DAMAGE.getAttrValue(casterAttr) / 10000.0f;
                }
            }
            hurt = hurt * (1 + positive);
        }

        // 怪物类型伤害减免
        if (caster.isMonster()) {
            float negative = 0;
            MonsterConfig monsterConfig = ConfigDataManager.getInstance().getById(MonsterConfig.class, caster.getConfigId());
            if (monsterConfig.getBossJob() > 0) {
                negative = getAttrValueByBossJob(monsterConfig.getBossJob(), targetAttr, true);
            } else {
                if (monsterConfig.getType() == MonsterType.COMMON_BOSS) {
                    negative = AttributeEnum.ZTXY_ELITE_BOSS_DAMAGE_REDUCTION.getAttrValue(targetAttr) / 10000.0f;
                }
                if (monsterConfig.getType() == MonsterType.JING_YING) {
                    negative = AttributeEnum.ZTXY_ELITE_DAMAGE_REDUCTION.getAttrValue(targetAttr) / 10000.0f;
                }
            }
            hurt = hurt * (1 - negative);
        }

        // 最终伤害加成倍率
        float lastHurtPercent = findAttrPercent(casterAttr, targetAttr, AttributeEnum.ZTXY_DAMAGE_BONUS);
        hurt *= lastHurtPercent;

        int suitPercent = findEquipSuitPercent(caster, effectConfig.getId());
        if (suitPercent > 0) {
            hurt += hurt * suitPercent / 10000f;
        }

        // 技能效果加成
        hurt = hurt * (hurtRate / 10000f) + fixHurt;
        hurt = calDamageAddHurt(context.addCfgList, hurt);

        // 暴击伤害
        if (RandomUtil.isGenerate(AttributeEnum.ZTXY_CRITICAL_RATE.getAttrValue(casterAttr))) {
            float criticalHurtPercent = findAttrPercent(casterAttr, targetAttr, AttributeEnum.ZTXY_CRITICAL_DAMAGE);
            hurt *= criticalHurtPercent;
            context.replaceFloaterType(FightConst.FloaterType.CRITICAL);
        }

        return (long) hurt;
    }

    /**
     * 伤害加成增加的伤害
     * @param addCfgList 伤害加成配置
     * @param hurt 基础伤害
     * @return  加成总伤害
     */
    private static float calDamageAddHurt(List<SkillEffectConfig> addCfgList, float hurt) {
        float addHurt = hurt;
        if (CollectionUtils.isEmpty(addCfgList)) {
            return addHurt;
        }

        for (SkillEffectConfig add : addCfgList) {
            List<int[]> addParameterList = add.getParameter();
            if (CollectionUtils.isEmpty(addParameterList)) {
                continue;
            }
            int[] addParameter = addParameterList.get(0);
            if (addParameter == null || addParameter.length == 0) {
                continue;
            }
            int addHurtRate = addParameter[0];
            int addFixHurt = 0;
            if (addParameter.length > 1) {
                addFixHurt = addParameter[1];
            }
            addHurt = addHurt * ((10000 + addHurtRate) / 10000f) + addFixHurt;

        }
        return addHurt;
    }

    private static float getAttrValueByBossJob(int bossJob, Attribute attr, boolean reduce) {
        Map<Integer, AttributeEnum> jobToReduceAttrMap = new HashMap<>();
        jobToReduceAttrMap.put(MonsterJobType.JOB1, AttributeEnum.ZTXY_JOB1_BOSS_DAMAGE_REDUCTION);
        jobToReduceAttrMap.put(MonsterJobType.JOB2, AttributeEnum.ZTXY_JOB2_BOSS_DAMAGE_REDUCTION);
        jobToReduceAttrMap.put(MonsterJobType.JOB3, AttributeEnum.ZTXY_JOB3_BOSS_DAMAGE_REDUCTION);
        jobToReduceAttrMap.put(MonsterJobType.JOB4, AttributeEnum.ZTXY_JOB4_BOSS_DAMAGE_REDUCTION);
        jobToReduceAttrMap.put(MonsterJobType.JOB5, AttributeEnum.ZTXY_JOB5_BOSS_DAMAGE_REDUCTION);

        Map<Integer, AttributeEnum> jobToIncreaseAttrMap = new HashMap<>();
        jobToIncreaseAttrMap.put(MonsterJobType.JOB1, AttributeEnum.ZTXY_JOB1_BOSS_DAMAGE);
        jobToIncreaseAttrMap.put(MonsterJobType.JOB2, AttributeEnum.ZTXY_JOB2_BOSS_DAMAGE);
        jobToIncreaseAttrMap.put(MonsterJobType.JOB3, AttributeEnum.ZTXY_JOB3_BOSS_DAMAGE);
        jobToIncreaseAttrMap.put(MonsterJobType.JOB4, AttributeEnum.ZTXY_JOB4_BOSS_DAMAGE);
        jobToIncreaseAttrMap.put(MonsterJobType.JOB5, AttributeEnum.ZTXY_JOB5_BOSS_DAMAGE);
        AttributeEnum attrEnum;
        if (reduce) {
            AttributeEnum defaultAttr = AttributeEnum.ZTXY_ELITE_BOSS_DAMAGE_REDUCTION;
            attrEnum = jobToReduceAttrMap.getOrDefault(bossJob, defaultAttr);
        } else {
            AttributeEnum defaultAttr = AttributeEnum.ZTXY_ELITE_BOSS_DAMAGE;
            attrEnum = jobToIncreaseAttrMap.getOrDefault(bossJob, defaultAttr);
        }
        return attrEnum.getAttrValue(attr) / 10000.0f;
    }

    private static float findAttrPercent(Attribute casterAttr, Attribute targetAttr, AttributeEnum attributeEnum) {
        long positiveValue = attributeEnum.getAttrValue(casterAttr);
        AttributeScoreCache cache = ConfigCacheManager.getInstance().getCache(AttributeScoreCache.class);
        AttributeEnum negativeAttrEnum = AttributeEnum.valueOf(cache.findReductionAttrId(attributeEnum.getType()));
        long negativeValue = 0;
        if (negativeAttrEnum != null) {
            negativeValue = negativeAttrEnum.getAttrValue(targetAttr);
        }
        float per = (10000 + positiveValue - negativeValue) / 10000f;
        return per <= 0 ? 1.0f : per;
    }

    public static int findEquipSuitPercent(Performer caster, int effectId) {
        Player player = PlayerManager.getInstance().getPlayer(caster.getId());
        if (player == null) {
            return 0;
        }
        PlayerActor actor = player.getActor();
        Set<Integer> set = actor.getEquipSuits();
        if (CollectionUtils.isEmpty(set)) {
            return 0;
        }
        for (int cid : set) {
            EquipSuitConfig config = ConfigDataManager.getInstance().getById(EquipSuitConfig.class, cid);
            if (config == null) {
                continue;
            }
            int[] arr = config.getEffectSpecial();
            if (arr == null || arr.length < 2) {
                continue;
            }
            return arr[1];
        }
        return 0;
    }

    /**
     * 通过map_level的配置对怪物的属性加成
     *
     * @param monsterConfig 怪物配置
     * @param map 地图
     * @return 属性
     */
    public static Attribute monsterFixAddPercent(MonsterConfig monsterConfig, GameMap map) {
        Attribute attribute = new Attribute();
        attribute.fixAdd(monsterConfig.getAttribute());

        if (map instanceof ZtxyBarrierDuplicate) {
            ZtxyBarrierDuplicate duplicate = (ZtxyBarrierDuplicate) map;
            MapLevelConfig mapLevelConfig = duplicate.getMapLevelConfig();
            if (mapLevelConfig != null && ArrayUtils.isNotEmpty(mapLevelConfig.getAttributeUp())) {
                int[] careers = monsterConfig.getBossJob() == RoleConst.Career.COMMON ? new int[]{RoleConst.Career.COMMON} : new int[]{monsterConfig.getBossJob(), RoleConst.Career.COMMON};
                int attributeArrayLen = mapLevelConfig.getAttributeUp().length;
                for (int i : careers) {
                    if (attributeArrayLen > i) {
                        Map<Integer, Long> attrMap = mapLevelConfig.getAttributeUp()[i];
                        if (attrMap == null) {
                            continue;
                        }
                        attrMap.forEach((k, v) ->
                                attribute.getAttributeMap().computeIfPresent(k, (key, value) -> value + (value * v / 10000))
                        );
                    }
                }
            }
        }

        return attribute;
    }
}
