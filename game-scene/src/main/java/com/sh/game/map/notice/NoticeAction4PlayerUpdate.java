package com.sh.game.map.notice;

import com.sh.game.common.communication.msg.map.*;
import com.sh.game.common.communication.msg.map.play.ResUseFightItemMessage;
import com.sh.game.common.communication.msg.system.orehole.ResDigStateMessage;
import com.sh.game.common.communication.msg.system.role.ResPlayerAttributeChangeMessage;
import com.sh.game.common.communication.msg.system.role.bean.AttributeBean;
import com.sh.game.common.communication.msg.system.roleMount.ResRoleMountInfoMessage;
import com.sh.game.common.communication.notice.*;
import com.sh.game.common.constant.AttributeConst;
import com.sh.game.common.constant.AttributeEnum;
import com.sh.game.common.constant.DailyConst;
import com.sh.game.common.constant.TeamConst;
import com.sh.game.common.entity.attr.Attribute;
import com.sh.game.common.entity.buff.Buff;
import com.sh.game.common.tips.CommonTips;
import com.sh.game.common.util.TimeUtil;
import com.sh.game.event.EventUtil;
import com.sh.game.map.MapUpdateManager;
import com.sh.game.map.Module;
import com.sh.game.map.Player;
import com.sh.game.map.SceneManager;
import com.sh.game.map.buff.BuffManager;
import com.sh.game.map.daily.DailyManager;
import com.sh.game.map.daily.controller.Controller;
import com.sh.game.map.daily.controller.type.CrossArenaController;
import com.sh.game.map.event.MapEventType;
import com.sh.game.map.fight.entity.FightResult;
import com.sh.game.map.move.MoveManager;
import com.sh.game.map.obj.*;
import com.sh.game.map.player.PlayerManager;
import com.sh.game.map.scene.GameMap;
import com.sh.game.map.teleport.TeleportManager;
import com.sh.game.map.util.GeomUtil;
import com.sh.game.map.util.ObjUtil;
import com.sh.game.map.util.TipUtil;
import com.sh.game.notice.NoticeAction;
import org.apache.commons.collections.CollectionUtils;

import java.util.List;
import java.util.Map;

@NoticeAction
public class NoticeAction4PlayerUpdate {


    public void SkillUpdateNotice(SkillUpdateNotice notice) {
        MapUpdateManager.getInstance().updateSkill(notice);
    }

    public void AttributeUpdateNotice(AttributeUpdateNotice notice) {
        Player player = PlayerManager.getInstance().getPlayer(notice.getRid());
        if (player == null || player.getActor() == null) {
            return;
        }
        if (notice.getActorId() == player.getId()) {
            Attribute before = player.getActor().getAttributeMap().getOrDefault(AttributeConst.AttributeType.Cate.BASE, new Attribute());
            Map<Integer, Long> changes = Attribute.compare(notice.getAttribute(), before);
            ResPlayerAttributeChangeMessage msg = new ResPlayerAttributeChangeMessage();
            AttributeBean bean = new AttributeBean();
            changes.forEach((k, v) -> {
                bean.getAttributeType().add(k);
                bean.getAttributeValue().add(v);
            });
            msg.setUid(player.getActor().getRid());
            msg.setActorId(player.getActor().getId());
            msg.setAttr(bean);
            msg.setPower(AttributeEnum.FIGHT_POWER.getAttrValue(notice.getAttribute()));
            msg.setShow(true);
            Module.MSG_TRANSFORMER.sendMsg(msg, player.getActor().getRid());
            player.getActor().getAttributeMap().put(AttributeConst.AttributeType.Cate.BASE, notice.getAttribute());
            player.getActor().attributeChange();
            EventUtil.fireEvent(MapEventType.Attribute_CHANGE, player);
            return;
        }
        HeroActor heroActor = player.getActor().getHeroActor();
        if (heroActor == null) {
            return;
        }
        heroActor.getAttributeMap().put(AttributeConst.AttributeType.Cate.BASE, notice.getAttribute());
        heroActor.attributeChange();
    }

    public void EquipUpdateNotice(EquipUpdateNotice notice) {
        long rid = notice.getRid();
        int itemId = notice.getItemId();
        int pos = notice.getPos();
        long actorId = notice.getActorId();
        long lid = notice.getLid();
        MapUpdateManager.getInstance().updateEquip(rid, actorId, pos, itemId, lid);
    }

    public void equipSuitUpdateNotice(EquipSuitUpdateNotice notice) {
        Player player = PlayerManager.getInstance().getPlayer(notice.getRid());
        if (player == null) {
            return;
        }
        PlayerActor actor = player.getActor();
        if (actor.getId() != notice.getActorId()) {
            actor = actor.getHeroActor();
        }
        if (actor == null) {
            return;
        }
        actor.setEquipSuits(notice.getSuitSet());
    }

    public void VipUpdateNotice(VipUpdateNotice notice) {
        long rid = notice.getRid();
        int vipLevel = notice.getVipLevel();
        MapUpdateManager.getInstance().updateVip(rid, vipLevel);
    }

    public void BuffAddNotice(BuffAddNotice notice) {
        int buffId = notice.getBuffId();
        long rid = notice.getRid();
        Player player = PlayerManager.getInstance().getPlayer(rid);
        PlayerActor playerActor = player.getActor();
        BuffManager.getInstance().append(playerActor, buffId, playerActor, null, TimeUtil.getNowOfMills());
        ResUseFightItemMessage msg = new ResUseFightItemMessage();
        Module.MSG_TRANSFORMER.sendMsg(msg, rid);
    }

    public void ScenceAddBuffNotice(ScenceAddBuffNotice notice) {
        GameMap map = SceneManager.getInstance().getMap(notice.getMapUid());
        Performer caster = (Performer) map.getObjectMap().get(notice.getCasterUid());
        Performer target = (Performer) map.getObjectMap().get(notice.getTargetUid());
        FightResult.BuffImmediateEffect effectRet = new FightResult.BuffImmediateEffect();
        effectRet.setFightEffectRet(new FightResult.FightEffectRet());
        effectRet.getFightEffectRet().hurt = notice.getHurt();
        for (int buffId : notice.getBuffIds()) {
            BuffManager.getInstance().append(target, buffId, caster, effectRet, notice.getTime());
        }
    }

    public void BufferAddNotice(BufferAddNotice notice) {
        List<Integer> monsterCfgId = notice.getMonsterCfgId();
        Player player = PlayerManager.getInstance().getPlayer(notice.getRid());
        PlayerActor actor = player.getActor();
        GameMap map = SceneManager.getInstance().getMap(actor);
        IMapObject nearestTarget = null;
        int dis = Integer.MAX_VALUE;
        List<IMapObject> roundList = map.getAoi().getObjectListByRangeAndType(actor.getPosition(), actor.getViewRange(), MapObjectType.MONSTER);
        if (!CollectionUtils.isEmpty(roundList)) {
            for (IMapObject mapObj : roundList) {
                if (!monsterCfgId.contains(mapObj.getConfigId())) {
                    continue;
                }

                int distance = GeomUtil.distance(actor.getPoint(), mapObj.getPoint());
                if (distance < dis) {
                    dis = distance;
                    nearestTarget = mapObj;
                }
            }
        }
        if (nearestTarget != null) {
            MonsterActor target = (MonsterActor) nearestTarget;
            if (target.getOwner() <= 0) {
                target.ownerChange(actor);
            }
            BuffManager.getInstance().append(target, notice.getBufferId(), actor, null, TimeUtil.getNowOfMills());
        }
    }

    /**
     * 队伍变更
     *
     * @param notice
     */
    public void TeamChangeNotice(TeamChangeNotice notice) {
        Player player = PlayerManager.getInstance().getPlayer(notice.getRid());
        if (player == null) {
            return;
        }
        PlayerActor actor = player.getActor();
        if (actor == null) {
            return;
        }
        long old = actor.getTeamID();
        player.getActorMap().forEach((k, v) -> v.setTeamID(notice.getTeamID()));
        if (notice.getTeamID() > 0) {
            actor.sendTeamHpMp();
            return;
        }
        if (TeamConst.TEAM_TYPE_ARENA == notice.getTeamType()) {
            CrossArenaController controller = (CrossArenaController) DailyManager.getInstance().getController(DailyConst.DailyType.CROSS_ARENA);
            controller.playerLeaveTeam(actor, old);
        }
        PlayerManager.getInstance().leaveChangeFightModel(actor, false);
    }

    /**
     * 队长变更
     *
     * @param notice
     */
    public void TeamLeaderNotice(TeamLeaderNotice notice) {
        Controller controller = DailyManager.getInstance().getController(DailyConst.DailyType.CROSS_ARENA);
        if (controller == null) {
            return;
        }
        CrossArenaController crossArena = (CrossArenaController) controller;
        crossArena.setTeamLeader(notice.getTeamID(), notice.getLeader());
    }

//    public void MountLeadUpdateNotice(MountUpdateNotice notice) {
//        Player player = PlayerManager.getInstance().getPlayer(notice.getRoleId());
//        if (player == null) {
//            return;
//        }
//        PlayerActor actor = player.getActor();
//        if (actor == null) {
//            return;
//        }
//
//        actor.setMount(notice.getMount());
//
//        ResPlayerMountChangeMessage msg = new ResPlayerMountChangeMessage();
//        msg.setLid(actor.getId());
//        msg.setMount(actor.getMount());
//        Module.MSG_TRANSFORMER.sendRoundMessage(msg, actor);
//    }

    public void TitleUpdateNotice(TitleUpdateNotice notice) {
        Player player = PlayerManager.getInstance().getPlayer(notice.getRoleId());
        if (player == null) {
            return;
        }
        PlayerActor actor = player.getActor();
        if (actor == null) {
            return;
        }
        actor.setTitle(notice.getTitle());
        ResPlayerTitleChangeMessage msg = new ResPlayerTitleChangeMessage();
        msg.setLid(player.getId());
        msg.setTitle(notice.getTitle());
        Module.MSG_TRANSFORMER.sendRoundMessage(msg, actor);
    }

    public void MingWangUpdateNotice(MingWangUpdateNotice notice) {
        Player player = PlayerManager.getInstance().getPlayer(notice.getRoleId());
        if (player == null) {
            return;
        }
        PlayerActor actor = player.getActor();
        if (actor == null) {
            return;
        }
        actor.setMingWang(notice.getMingWang());
        ResPlayerMingWangChangeMessage msg = new ResPlayerMingWangChangeMessage();
        msg.setLid(player.getId());
        msg.setMingWang(notice.getMingWang());
        Module.MSG_TRANSFORMER.sendRoundMessage(msg, actor);
    }

    public void MountUpdateNotice(MountUpdateNotice notice) {
        Player player = PlayerManager.getInstance().getPlayer(notice.getRoleId());
        if (player == null) {
            return;
        }
        PlayerActor actor = player.getActor();
        if (actor == null) {
            return;
        }

        actor.setMount(notice.getMount());

        ResPlayerMountChangeMessage msg = new ResPlayerMountChangeMessage();
        msg.setLid(actor.getRid());
        msg.setMount(actor.getMount());
        Module.MSG_TRANSFORMER.sendRoundMessage(msg, actor);

        ResRoleMountInfoMessage infoMsg = new ResRoleMountInfoMessage();
        infoMsg.setRidingStatus(notice.getMount() > 0);
        Module.MSG_TRANSFORMER.sendMsg(infoMsg, notice.getRoleId());
    }

    /**
     * 治疗
     *
     * @param notice
     */
    public void CureNotice(CureNotice notice) {
        PlayerManager.getInstance().playerCure(notice.getRid());
    }

    /**
     * 清理红名
     *
     * @param notice
     */
    public void CleanPkvalueNotice(CleanPkvalueNotice notice) {
        Player player = PlayerManager.getInstance().getPlayer(notice.getRid());
        if (player == null) {
            return;
        }
        PlayerActor actor = player.getActor();
        if (actor == null) {
            return;
        }
        int pkValue = actor.getPkValue() - notice.getDeduct();
        if (pkValue < 0) {
            pkValue = 0;
        }
        PlayerManager.getInstance().changePkValue(actor, pkValue);
        TipUtil.show(notice.getRid(), CommonTips.场景_善恶值清除成功);
    }

    /**
     * 挖矿传送
     *
     * @param notice
     */
    public void OreHoleTelportNotice(OreHoleTelportNotice notice) {
        long rid = notice.getRid();
        Player player = PlayerManager.getInstance().getPlayer(rid);
        if (player == null) {
            return;
        }
        PlayerActor actor = player.getActor();
        if (actor == null) {
            return;
        }
        MoveManager.getInstance().changeDir(rid, (byte) notice.getFace());
        TeleportManager.getInstance().teleport(actor, notice.getMap(), notice.getX(), notice.getY());
    }

    /**
     * 更新挖矿
     *
     * @param notice
     */
    public void OreHoleUdateNotice(OreHoleUpdateNotice notice) {
        long rid = notice.getRid();
        Player player = PlayerManager.getInstance().getPlayer(rid);
        if (player == null) {
            return;
        }
        PlayerActor actor = player.getActor();
        if (actor == null) {
            return;
        }
        actor.setDig(notice.isDig());

        ResDigStateMessage msg = new ResDigStateMessage();
        msg.setRoleId(rid);
        msg.setState(notice.isDig() ? 1 : 0);
        Module.MSG_TRANSFORMER.sendRoundMessage(msg, actor);
    }

    /**
     * 更新祭坛次数
     *
     * @param notice
     */
    public void JiTanRoleCountNotice(JiTanRoleCountNotice notice) {
        long rid = notice.getRid();
        Player player = PlayerManager.getInstance().getPlayer(rid);
        if (player == null) {
            return;
        }
        PlayerActor actor = player.getActor();
        if (actor == null) {
            return;
        }
        actor.setJiTanBossCount(notice.getCount());
    }

    public void roleSuperManUpdate(RoleSuperManUpdateNotice roleSuperManUpdateNotice) {
        long rid = roleSuperManUpdateNotice.getRoleId();
        Player player = PlayerManager.getInstance().getPlayer(rid);
        if (player == null) {
            return;
        }
        PlayerActor actor = player.getActor();
        if (actor == null) {
            return;
        }
        actor.setSuperMan(roleSuperManUpdateNotice.isSuperMan());
        actor.setProtect(roleSuperManUpdateNotice.isProtect());
        actor.setRandomProtect(roleSuperManUpdateNotice.isRandomProtect());
        ResPlayerSuperManChangeMessage msg = new ResPlayerSuperManChangeMessage();
        msg.setLid(player.getId());
        msg.setIsSuperMan(roleSuperManUpdateNotice.isSuperMan() ? 1 : 0);
        Module.MSG_TRANSFORMER.sendRoundMessage(msg, actor);
    }

    /**
     * 更新玩家神威boss每日掉落次数
     *
     * @param notice 更新玩家神威boss每日掉落次数消息
     */
    public void shenWeiBossDailyDropCountNotice(ShenWeiBossDailyDropCountRetNotice notice) {
        long roleId = notice.getRoleId();
        Player player = PlayerManager.getInstance().getPlayer(roleId);
        if (player == null) {
            return;
        }
        PlayerActor actor = player.getActor();
        if (actor == null) {
            return;
        }
        actor.setShenWeiBossDailyDropCount(notice.getCount());
    }

    /**
     * 更新沙捐展示buff
     * @param notice
     */
    public void updateJuanXianBuff(JuanXianBuffNotice notice){
        long roleId = notice.getRoleId();
        Player player = PlayerManager.getInstance().getPlayer(roleId);
        if (player == null) {
            return;
        }
        PlayerActor actor = player.getActor();
        if (actor == null) {
            return;
        }
        if(notice.getOldBuffId() > 0){
            BuffManager.getInstance().remove(actor, notice.getOldBuffId());
        }
        boolean append = BuffManager.getInstance().append(actor, notice.getBuffId(), actor, null, TimeUtil.getNowOfMills());
        if (append) {
            Map<Integer, Buff> buffMap = actor.getBuffs().getBuffMap();
            actor.saveBuff(buffMap);
        }
    }

    /**
     * 复活
     *
     * @param notice
     */
    public void relive(ReliveNotice notice) {
        long roleId = notice.getRoleId();
        Player player = PlayerManager.getInstance().getPlayer(roleId);
        if (player == null) {
            return;
        }
        PlayerActor actor = player.getActor();
        if (!actor.isDead()) {
            return;
        }
        GameMap map = SceneManager.getInstance().getMap(player.getActor());
        if (map == null) {
            return;
        }
        PlayerManager.getInstance().relive(map, actor, notice.getRelivePercent());
    }


    public void YuPeiUpdateNotice(YuPeiUpdateNotice notice) {
        Player player = PlayerManager.getInstance().getPlayer(notice.getRoleId());
        if (player == null) {
            return;
        }
        PlayerActor actor = player.getActor();
        if (actor == null) {
            return;
        }
        actor.setYuPei(notice.getYuPeiId());
    }

    /**
     * 更新玩家神威boss每日掉落次数
     *
     * @param notice 更新玩家神威boss每日掉落次数消息
     */
    public void updateGoldDailyCountNotice(GoldDailyRetNotice notice) {
        long roleId = notice.getRoleId();
        Player player = PlayerManager.getInstance().getPlayer(roleId);
        if (player == null) {
            return;
        }
        PlayerActor actor = player.getActor();
        if (actor == null) {
            return;
        }
        actor.setGoldCount(notice.getCount());
    }

    public void RoleCampTypeNotice(RoleCampTypeNotice notice) {
        Player player = PlayerManager.getInstance().getPlayer(notice.getRoleId());
        if (player == null) {
            return;
        }
        PlayerActor actor = player.getActor();
        if (actor == null) {
            return;
        }
        actor.setCampType(notice.getCampType());

        ResPlayerCampTypeChangeMessage msg = new ResPlayerCampTypeChangeMessage();
        msg.setLid(player.getId());
        msg.setCampType(notice.getCampType());
        Module.MSG_TRANSFORMER.sendRoundMessage(msg, actor);

        PlayerManager.getInstance().leaveCampChangeFightModel(actor);
    }

    public void MilitaryRankLevelChange(MilitaryRankLevelChangeNotice notice) {
        Player player = PlayerManager.getInstance().getPlayer(notice.getRoleId());
        if (player == null) {
            return;
        }
        PlayerActor actor = player.getActor();
        if (actor == null) {
            return;
        }
        actor.setMilitaryRankCfgId(notice.getMilitaryRankCfgId());

        ResPlayerMilitaryRankChangeMessage msg = new ResPlayerMilitaryRankChangeMessage();
        msg.setLid(player.getId());
        msg.setMilitaryRankCfgId(notice.getMilitaryRankCfgId());
        Module.MSG_TRANSFORMER.sendRoundMessage(msg, actor);
    }

    public void realmUpdateNotice(RealmUpdateNotice notice) {
        Player player = PlayerManager.getInstance().getPlayer(notice.getRoleId());
        if (player == null) {
            return;
        }
        PlayerActor actor = player.getActor();
        if (actor == null) {
            return;
        }
        actor.setRealmId(notice.getRealmId());
        ResUpdateViewMessage msg = new ResUpdateViewMessage();
        msg.getAddPlayers().add(ObjUtil.packRoundPlayer(actor));
        Module.MSG_TRANSFORMER.sendRoundMessage(msg, actor);
    }
}
