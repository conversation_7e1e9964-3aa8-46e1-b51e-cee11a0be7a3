package com.sh.game.common.entity.usr;

import com.sh.game.common.persist.PersistDaoTemplate;
import com.sh.game.common.persist.dao.PersistSimpleDao;
import com.sh.game.system.huoban.entity.HuoBan;
import com.sh.game.system.huoban.entity.HuoBanFormation;
import com.sh.game.system.huoban.entity.RefreshHuoBan;
import io.protostuff.Tag;
import lombok.Getter;
import lombok.Setter;
import org.apache.commons.collections.MapUtils;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Getter
@Setter
@PersistDaoTemplate(PersistSimpleDao.class)
public class RoleHuoBan extends AbstractRoleEntity {

    /**
     * rid
     */
    @Tag(1)
    private long id;

    /**
     * 伙伴背包
     * k 伙伴配置id
     */
    @Tag(2)
    private Map<Integer, HuoBan> huoBanBag = new HashMap<>();

    /**
     * 各品质伙伴抽取次数
     * k:伙伴品质 v:伙伴抽取次数
     */
    @Tag(3)
    private Map<Integer, Integer> qualityRefreshMap = new HashMap<>();

    /**
     * 伙伴编队
     * k:编队索引 v:{位置索引,伙伴配置id}
     */
    @Tag(4)
    private Map<Integer, Map<Integer, Integer>> formations = new HashMap<>();

    /**
     * 当前正在使用的编队索引
     */
    @Tag(5)
    private int inUseFormation = 1;

    /**
     * 暴击增幅
     */
    @Tag(6)
    private Map<Integer, Long> criticalHit = new HashMap<>();


    public Map<Integer, Integer> findFormationsByIndex(int index){
        if(index <= 0){
            return null;
        }
        return formations.computeIfAbsent(index, k -> new HashMap<>());
    }

    public Map<Integer, Integer> findInUseFormation() {
        return formations.computeIfAbsent(inUseFormation, k -> new HashMap<>());
    }

    public int getLevelCount() {
        if (MapUtils.isEmpty(huoBanBag)) {
            return 0;
        }
        return huoBanBag.values().stream().mapToInt(HuoBan::getLevel).sum();
    }

}
