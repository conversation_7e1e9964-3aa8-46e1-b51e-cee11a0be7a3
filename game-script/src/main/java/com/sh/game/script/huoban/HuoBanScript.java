package com.sh.game.script.huoban;

import com.sh.common.config.ConfigDataManager;
import com.sh.commons.util.Symbol;
import com.sh.game.common.communication.msg.system.huoban.ResHuoBanInfoMessage;
import com.sh.game.common.communication.msg.system.huoban.bean.HuoBanBean;
import com.sh.game.common.communication.msg.system.huoban.bean.HuoBanFormationBean;
import com.sh.game.common.communication.msg.system.huoban.bean.HuoBanFormationSlotBean;
import com.sh.game.common.config.converter.JinHaoAndShuXianMapLongConverter;
import com.sh.game.common.config.model.HuoBanConfig;
import com.sh.game.common.config.model.HuoBanLevelConfig;
import com.sh.game.common.constant.*;
import com.sh.game.common.entity.attr.Attribute;
import com.sh.game.common.entity.backpack.BackpackStash;
import com.sh.game.common.entity.usr.Role;
import com.sh.game.common.entity.usr.RoleHuoBan;
import com.sh.game.common.util.BackPackStashUtil;
import com.sh.game.common.util.GlobalUtil;
import com.sh.game.common.util.MessageUtil;
import com.sh.game.data.DataCenter;
import com.sh.game.event.IEventOnRoleAttributeCountScript;
import com.sh.game.event.IEventOnRoleCreate;
import com.sh.game.system.goal.GoalManager;
import com.sh.game.system.huoban.entity.HuoBan;
import com.sh.game.system.huoban.script.IHuoBanScript;
import com.sh.game.system.rewardinfo.RewardInfoManager;
import com.sh.game.system.skill.SkillManager;
import com.sh.script.annotation.Script;
import lombok.extern.slf4j.Slf4j;

import java.util.*;

/**
 * <AUTHOR>
 * @date 2023/10/10 17:08
 */
@Script
@Slf4j
public class HuoBanScript implements IHuoBanScript, IEventOnRoleAttributeCountScript, IEventOnRoleCreate {

    @Override
    public void info(Role role) {
        RoleHuoBan huoBan = role.findHuoBan();

        ResHuoBanInfoMessage msg = new ResHuoBanInfoMessage();
        msg.setInUseFormation(huoBan.getInUseFormation());
        msg.setCriticalHit(huoBan.getCriticalHit().values().stream().mapToLong(i -> i).sum());
        huoBan.getFormations().forEach((index,formation)->{
            HuoBanFormationBean formationBean = new HuoBanFormationBean();
            formationBean.setIndex(index);
            formation.forEach((slot,id)->{
                HuoBanFormationSlotBean slotBean = new HuoBanFormationSlotBean();
                slotBean.setIndex(slot);
                slotBean.setConfigId(id);
                formationBean.getFormations().add(slotBean);
            });
            msg.getFormations().add(formationBean);
        });
        for (Map.Entry<Integer, HuoBan> entry : huoBan.getHuoBanBag().entrySet()) {
            HuoBan ban = entry.getValue();
            HuoBanBean bean = new HuoBanBean();
            bean.setConfigId(entry.getKey());
            bean.setLevel(ban.getLevel());
            msg.getHuoBan().add(bean);
        }

        MessageUtil.sendMsg(msg, role);
    }

    @Override
    public void huoBanLevelUp(Role role, int configId) {
        HuoBanConfig config = ConfigDataManager.getInstance().getById(HuoBanConfig.class, configId);
        if (config == null) {
            log.error("伙伴系统-玩家升级伙伴-找不到伙伴配置-玩家id:{},name:{},配置id:{}", role.getId(), role.getName(), configId);
            return;
        }
        RoleHuoBan huoBan = role.findHuoBan();
        Map<Integer, HuoBan> huoBanBag = huoBan.getHuoBanBag();
        HuoBan ban = huoBanBag.computeIfAbsent(configId, k -> new HuoBan());
        HuoBanLevelConfig huoBanLevelConfig = ConfigDataManager.getInstance().getById(HuoBanLevelConfig.class, configId + Symbol.JINHAO + ban.getLevel());
        if (huoBanLevelConfig == null) {
            log.error("伙伴系统-玩家升级伙伴-找不到伙伴等级配置-玩家id:{},name:{},配置id:{},等级:{}", role.getId(), role.getName(), configId, ban.getLevel());
            return;
        }
        if (huoBanLevelConfig.getNextLevel() <= 0) {
            log.error("伙伴系统-玩家升级伙伴-伙伴已满级-玩家id:{},name:{},配置id:{},等级:{}", role.getId(), role.getName(), configId, ban.getLevel());
            return;
        }

        // 先判断碎片道具够不够 不够直接退出
        // 再判断职业经验道具够不够 是否还有全职经验抵扣
        BackpackStash stash = new BackpackStash(role);
        for (Map.Entry<Integer, Long> entry : huoBanLevelConfig.getCost().entrySet()) {
            long fetchCount = role.getBackpack().fetchCountLByCfgId(entry.getKey());
            if (fetchCount < entry.getValue()) {
                if (entry.getKey() == config.getCall_recycle().get(0)[0]) {
                    log.error("伙伴系统-玩家升级伙伴-碎片道具不足-玩家id:{},name:{},配置id:{},等级:{}", role.getId(), role.getName(), configId, ban.getLevel());
                    return;
                }
                long diff = entry.getValue() - fetchCount;
                int allExpItemId = GlobalUtil.getGlobalInt(GameConst.GlobalId.HUO_BAN_ALL_EXP_ITEM_ID);
                long allExpItemCount = role.getBackpack().fetchCountLByCfgId(allExpItemId);
                if (allExpItemCount < diff) {
                    log.error("伙伴系统-玩家升级伙伴-升级经验不足-玩家id:{},name:{},配置id:{},等级:{}", role.getId(), role.getName(), configId, ban.getLevel());
                    return;
                }
                stash.decrease(allExpItemId, diff);
                stash.decrease(entry.getKey(), fetchCount);
            } else {
                stash.decrease(entry.getKey(), entry.getValue());
            }
        }
        if (!stash.commit(role, LogAction.HUO_BAN_LEVEL_UP_COST)) {
            log.error("伙伴系统-玩家升级伙伴-扣除道具失败-玩家id:{},name:{},配置id:{},等级:{}", role.getId(), role.getName(), configId, ban.getLevel());
            return;
        }

        Map<Integer, Long> qualityCriticalHit = GlobalUtil.find(GameConst.GlobalId.HUO_BAN_QUALITY_CRITICAL_HIT, JinHaoAndShuXianMapLongConverter.class);
        if (qualityCriticalHit.containsKey(config.getQuality())) {
            int[] initCriticalHitValue = GlobalUtil.findJingHaoIntArray(GameConst.GlobalId.HUO_BAN_CRITICAL_HIT_INIT_VALUE);
            huoBan.getCriticalHit().merge(initCriticalHitValue[0], qualityCriticalHit.get(config.getQuality()), Long::sum);
        }

        ban.setLevel(huoBanLevelConfig.getNextLevel());
        ban.getSkillIds().add(huoBanLevelConfig.getUnlockSkill());
        DataCenter.updateData(huoBan);
        GoalManager.getInstance().updateGoal(role, GoalConst.GoalType.SHENG_JI_HUO_BAN);

        // 刷新玩家技能
        SkillManager.getInstance().updateRoleSkill(role);
        info(role);
        onRoleAttributeChange(role);
    }

    /**
     * 变更伙伴状态
     *
     * @param role 角色
     * @param configId 伙伴配置id，如果是下阵则传0
     * @param index 上阵位置，从1开始
     * @param lineupIndex 队伍索引，从1开始
     */
    @Override
    public void huoBanState(Role role, int configId, int index,int lineupIndex) {
        updateState(role, configId, index, lineupIndex);
        onRoleAttributeChange(role);
        info(role);
        // 刷新玩家技能
        SkillManager.getInstance().updateRoleSkill(role);
    }

    private void updateState(Role role, int configId, int index, int lineupIndex){
        int[] formationLimit = GlobalUtil.findJingHaoIntArray(GameConst.GlobalId.HUO_BAN_FORMATION_LIMIT);
        // 最大只有几个编队
        if (lineupIndex <= 0 || lineupIndex > formationLimit[0]) {
            log.error("伙伴系统-玩家请求伙伴上阵-编队索引错误,玩家id:{},name:{},编队索引:{}", role.getId(), role.getName(), lineupIndex);
            return;
        }
        // 编队最多几个伙伴
        if (index <= 0 || index > formationLimit[1]) {
            log.error("伙伴系统-玩家请求伙伴上阵-上阵状态错误，玩家id:{},name:{},编队索引:{},index:{}", role.getId(), role.getName(), lineupIndex, index);
            return;
        }
        Map<Integer, Integer> shangZhenMap = role.findHuoBan().findFormationsByIndex(lineupIndex);
        shangZhenMap.put(index, configId);
        DataCenter.updateData(role.findHuoBan());
        GoalManager.getInstance().updateGoal(role, GoalConst.GoalType.SHANG_ZHEN_HUO_BAN);
    }

    @Override
    public AttributeConst.AttributeType getAttributeType() {
        return AttributeConst.AttributeType.Role.HUO_BAN;
    }

    @Override
    public void onRoleAttributeCount(Role role) {
        Attribute attribute = new Attribute();
        calHuoBanAttr(role, attribute);
        role.getAttributes().put(getAttributeType(), attribute);
    }

    @Override
    public void calHuoBanAttr(Role role, Attribute attribute) {
        RoleHuoBan huoBan = role.findHuoBan();
        // 暴击增幅
        attribute.fixAdd(huoBan.getCriticalHit());

        Map<Integer, HuoBan> huoBanBag = huoBan.getHuoBanBag();
        huoBanBag.forEach((id, ban) -> {
            HuoBanLevelConfig huoBanLevelConfig = ConfigDataManager.getInstance().getById(HuoBanLevelConfig.class, id + Symbol.JINHAO + ban.getLevel());
            attribute.fixAdd(huoBanLevelConfig.getLevel_attr());
        });
    }

    @Override
    public void huoBanItemJiHuo(Role role, int configId) {
        RoleHuoBan huoBan = role.findHuoBan();
        HuoBanConfig config = ConfigDataManager.getInstance().getById(HuoBanConfig.class, configId);
        if (config == null) {
            log.error("伙伴系统-玩家伙伴道具激活-伙伴配置不存在-玩家id:{},name:{},伙伴id:{}", role.getId(), role.getName(), configId);
            return;
        }
        huoBanGet(role, huoBan, config);
    }

    private void huoBanGet(Role role, RoleHuoBan huoBan, HuoBanConfig config) {
        Map<Integer, HuoBan> huoBanBag = huoBan.getHuoBanBag();
        if (huoBanBag.containsKey(config.getId())) {
            BackpackStash add = new BackpackStash(role);
            add.increase(config.getCall_recycle());
            add.commitToMail2(role.getId(), EmailConst.MailId.BACK_OVER_FLOW, false, EmailConst.MailId.BACK_OVER_FLOW);
            // 发送前端奖励
            RewardInfoManager.getInstance().resRewardInfo(role, RewardInfoTypeConst.NORMAL, config.getCall_recycle());
            log.info("伙伴系统-玩家召唤伙伴-玩家召唤重复伙伴,玩家id:{},name:{},伙伴id:{}", role.getId(), role.getName(), config.getId());
        } else {
            HuoBan ban = new HuoBan();
            ban.setLevel(1);
            ban.getSkillIds().add(1);
            huoBanBag.put(config.getId(), ban);
            log.info("伙伴系统-玩家召唤伙伴-玩家召唤伙伴,玩家id:{},name:{},伙伴id {}", role.getId(), role.getName(), config.getId());
            onRoleAttributeChange(role);
            // 刷新玩家技能
            SkillManager.getInstance().updateRoleSkill(role);
        }
        DataCenter.updateData(huoBan);

        GoalManager.getInstance().updateGoal(role, GoalConst.GoalType.ZHAO_HUAN_HUO_BAN);
        info(role);

    }

    @Override
    public int updateShangZhenTask(Role role) {
        Map<Integer, Integer> shangZhenMap = role.findHuoBan().findInUseFormation();
        return (int) shangZhenMap.values().stream().filter(x -> x != 0).count();
    }

    @Override
    public void changeFormation(Role role, int lineupIndex) {
        if(lineupIndex <= 0){
            return;
        }
        RoleHuoBan huoBan = role.findHuoBan();
        huoBan.setInUseFormation(lineupIndex);
        DataCenter.updateData(huoBan);
        info(role);
        onRoleAttributeChange(role);
        // 刷新玩家技能
        SkillManager.getInstance().updateRoleSkill(role);
    }

    @Override
    public void onRoleCreate(Role role) {
        RoleHuoBan roleHuoBan = role.findHuoBan();
        // 设置初始暴击增幅
        int[] initCriticalHitValue = GlobalUtil.findJingHaoIntArray(GameConst.GlobalId.HUO_BAN_CRITICAL_HIT_INIT_VALUE);
        roleHuoBan.getCriticalHit().put(initCriticalHitValue[0], (long) initCriticalHitValue[1]);

        int[] formationLimit = GlobalUtil.findJingHaoIntArray(GameConst.GlobalId.HUO_BAN_FORMATION_LIMIT);
        int[] formationInit = GlobalUtil.findJingHaoIntArray(GameConst.GlobalId.HUO_BAN_FORMATION_ID);
        // 初始化获得英雄
        for (int value : formationInit) {
            HuoBanConfig huoBanConfig = ConfigDataManager.getInstance().getById(HuoBanConfig.class, value);
            if (huoBanConfig == null) {
                log.error("伙伴系统-伙伴初始化获得-伙伴表ID:{}未配置", value);
                continue;
            }
            HuoBan huoBan = roleHuoBan.getHuoBanBag().computeIfAbsent(value, k -> new HuoBan());
            huoBan.setLevel(1);
            huoBan.getSkillIds().add(huoBanConfig.getSkillId());
        }
        // 初始化编队
        for (int i = 1; i <= formationLimit[0]; i++) {
            Map<Integer, Integer> formations = roleHuoBan.getFormations().computeIfAbsent(i, x -> new HashMap<>());
            for (int j = 1; j <= formationLimit[1]; j++) {
                formations.put(j, formationInit[j - 1]);
            }
        }

        roleHuoBan.setInUseFormation(1);
        DataCenter.updateData(roleHuoBan);

        // 刷新玩家技能
        SkillManager.getInstance().updateRoleSkill(role);
    }
}

