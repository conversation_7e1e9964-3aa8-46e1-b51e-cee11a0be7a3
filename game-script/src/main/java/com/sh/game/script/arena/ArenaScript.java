package com.sh.game.script.arena;

import com.sh.common.config.ConfigCacheManager;
import com.sh.common.config.ConfigDataManager;
import com.sh.game.common.config.cache.ArenaRankConfigCache;
import com.sh.game.common.config.model.ArenaRankConfig;
import com.sh.game.common.config.model.ArenaRobotConfig;
import com.sh.game.common.config.model.RoBotConfig;
import com.sh.game.common.constant.*;
import com.sh.game.common.entity.backpack.BackpackStash;
import com.sh.game.common.entity.sys.ArenaRankData;
import com.sh.game.common.entity.usr.Role;
import com.sh.game.common.util.*;
import com.sh.game.common.util.condition.ConditionUtil;
import com.sh.game.data.DataCenter;
import com.sh.game.data.SysDataProvider;
import com.sh.game.event.IEventOnRoleMidnightScript;
import com.sh.game.event.IEventOnServerStartUpScript;
import com.sh.game.event.IEventScheduleUpdateOnHourScript;
import com.sh.game.event.IEventScheduleUpdateOnMidnightScript;
import com.sh.game.system.arena.constant.ArenaConst;
import com.sh.game.system.arena.entity.ArenaLog;
import com.sh.game.system.arena.entity.ArenaRoleData;
import com.sh.game.system.arena.entity.RoleArena;
import com.sh.game.system.arena.script.IArenaScript;
import com.sh.game.system.mail.MailManager;
import com.sh.game.system.mgrobot.entity.MGRobotData;
import com.sh.game.system.query.QueryManager;

import com.sh.script.annotation.Script;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;

import java.util.*;
import java.util.concurrent.ConcurrentSkipListMap;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2023/9/19
 */
@Slf4j
@Script
public class ArenaScript implements IArenaScript, IEventScheduleUpdateOnMidnightScript, IEventOnRoleMidnightScript,
        IEventScheduleUpdateOnHourScript, IEventOnServerStartUpScript {

    @Override
    public void reqArenaInfo(Role role) {
        // 验证玩家是否达到解锁条件
        List<int[]> conditions = GlobalUtil.findConditions(GameConst.GlobalId.ARENA_UNLOCK_CONDITION);
        if (!ConditionUtil.validate(role, conditions)) {
            log.error("竞技场-请求信息-玩家不满足竞技场解锁条件,roleId:{},roleName:{}", role.getId(), role.getName());
            return;
        }
        // 验证玩家是否解锁
        if (!isUnlockArena(role.getRoleId())) {
            // 解锁玩家竞技场
            unlockArena(role);
            return;
        }

        RoleArena roleArena = role.findArena();
        if (roleArena.getRivals().isEmpty()) {
            // 更新对手
            refreshRival(role);
        }

        sendArenaInfo(role);
    }

    @Override
    public void reqArenaBattle(Role role, long roleId, long battleId) {
        // 验证玩家是否解锁
        if (!isUnlockArena(role.getRoleId())) {
            log.error("竞技场-挑战-玩家未解锁竞技场,roleId:{},roleName:{}", role.getId(), role.getName());
            return;
        }

        // 验证是否在结算时间
        int now = TimeUtil.getNowOfSeconds();
        if (isInSettleTime(now)) {
            log.error("竞技场-挑战-在结算时间,roleId:{},roleName:{}", role.getId(), role.getName());
            return;
        }

        // 验证对手是否在列表中
        RoleArena roleArena = role.findArena();
        if (battleId > 0) {
            Map<Long, ArenaLog> battleLog = roleArena.getBattleLog();
            ArenaLog arenaLog = battleLog.get(battleId);
            if (arenaLog == null || arenaLog.isChallenge() || arenaLog.getAttackerId() != roleId) {
                log.error("竞技场-挑战-玩家不可反击对手,roleId:{},roleName:{},rivalRoleId:{}", role.getId(), role.getName(), roleId);
                return;
            }
        } else {
            Set<Long> rivals = roleArena.getRivals();
            if (!rivals.contains(roleId)) {
                log.error("竞技场-挑战-玩家不在对手列表中,roleId:{},roleName:{},rivalRoleId:{}", role.getId(), role.getName(), roleId);
                return;
            }
        }
        Role rival = DataCenter.get(Role.class, roleId);
        if (rival == null) {
            ArenaRankData arenaRankData = SysDataProvider.get(ArenaRankData.class);
            MGRobotData robotData = arenaRankData.getRobotMap().getOrDefault(roleId, null);
            if (robotData == null) {
                log.error("竞技场-挑战-对手不存在,roleId:{},roleName:{},rivalRoleId:{}", role.getId(), role.getName(), roleId);
                return;
            }
            battleWithRobot(role, robotData, battleId); //对手是机器人的处理
            return;
        }

        // 挑战消耗
        List<int[]> itemCost = GlobalUtil.findItemCost(GameConst.GlobalId.ARENA_BATTLE_COST);
        BackpackStash stash = new BackpackStash(role);
        stash.decrease(itemCost);
        if (!stash.commit(role, LogAction.ARENA_BATTLE_COST)) {
            log.error("竞技场-挑战-材料不足,roleId:{},roleName:{}", role.getId(), role.getName());
            return;
        }

        onBattleEnd(role, roleId, 0, false, battleId);
    }

    private void battleWithRobot(Role role, MGRobotData robotData, long battleId) {
        // 挑战消耗
        List<int[]> itemCost = GlobalUtil.findItemCost(GameConst.GlobalId.ARENA_BATTLE_COST);
        BackpackStash stash = new BackpackStash(role);
        stash.decrease(itemCost);
        if (!stash.commit(role, LogAction.ARENA_BATTLE_COST)) {
            log.error("竞技场-挑战-材料不足,roleId:{},roleName:{}", role.getId(), role.getName());
            return;
        }

        log.info("竞技场-挑战-玩家请求战斗,roleId:{},roleName:{},rivalRoleId:{}", role.getId(), role.getName(), robotData.getId());

    }

    public void onBattleEnd(Role role, long rivalId, long battleId, boolean success, long revengeBattleId) {
//        ArenaRankData arenaRankData = SysDataProvider.get(ArenaRankData.class);
//        int nowOfSeconds = TimeUtil.getNowOfSeconds();
//
//        Role rival = null;
//        String name;
//        if (arenaRankData.getRobotMap().containsKey(rivalId)) {
//            MGRobotData robotData = arenaRankData.getRobotMap().get(rivalId);
//            RoBotConfig config = ConfigDataManager.getInstance().getById(RoBotConfig.class, robotData.getCfgId());
//            name = config.getName();
//        } else {
//            rival = DataCenter.get(Role.class, rivalId);
//            name = rival.getName();
//        }
//
//        int score = arenaRankData.getScore(role.getId());
//        int rivalScore = arenaRankData.getScore(rivalId);
//        int addScore = (int) Math.ceil(rivalScore * 12.0 / 1000);
//        int subScore = (int) -Math.ceil(rivalScore * 6.0 / 1000);
//
//        // 插入日志
//        if (rival != null) {
//            addBattleLog(rival, battleId, success, role.getId(), rival.getId(), subScore, nowOfSeconds
//                    , role.getAttribute().calculateFightPower(role.getCareer(), role.getLevel())
//                    , rival.getAttribute().calculateFightPower(rival.getCareer(), rival.getLevel()));
//        }
//
//        if (!success) {
//            sendArenaBattleInfo(role, score, 0, rivalScore, 0);
//            log.error("竞技场-挑战-战斗失败,roleId:{},roleName:{},rivalRoleId:{},rivalRoleName:{}", role.getId(), role.getName(), rivalId, name);
//            // 更新对手
//            refreshRival(role);
//            return;
//        }
//        CountManager.getInstance().count(role, CountConst.CountType.ARENA_SUCCESS_COUNT);
//        // 挑战奖励
//        List<int[]> reward = GlobalUtil.findItemCost(GameConst.GlobalId.ARENA_BATTLE_REWARD);
//        FriendData data = role.findNormal().getFriendDataMap().get(105);
//        if (data != null) {
//            int rate = 0;
//            rate += data.findBuffParam();
//            if (RandomUtil.isGenerate(rate)) {
//                int[] extraReward = {GlobalUtil.getGlobalInt(GameConst.GlobalId.ARENA_EXTRA_REWARD), 1};
//                reward.add(extraReward);
//                log.info("道友特权，触发斗技场额外奖励，玩家id {} name {}", role.getId(), role.getName());
//            }
//        }
//        BackpackStash stash = new BackpackStash(role);
//        stash.increase(reward);
//        if (!stash.commit(role, LogAction.ARENA_BATTLE_REWARD)) {
//            MailManager.getInstance().sendMail(role.getId(), EmailConst.MailId.BACK_OVER_FLOW, EmailConst.toMailAttach(reward, LogAction.ARENA_BATTLE_REWARD));
//        }
//
//        // 更新积分
//        arenaRankData.addScore(role.getId(), addScore, nowOfSeconds);
//        arenaRankData.addScore(rivalId, subScore, nowOfSeconds);
//
//        DataCenter.updateData(arenaRankData);
//
//        // 更新战报复仇状态
//        if (revengeBattleId > 0) {
//            RoleArena roleArena = role.findArena();
//            Map<Long, ArenaLog> battleLog = roleArena.getBattleLog();
//            ArenaLog arenaLog = battleLog.get(revengeBattleId);
//            arenaLog.setChallenge(true);
//            DataCenter.updateData(roleArena);
//        }
//
//        // 更新对手
//        refreshRival(role);
//
//        sendArenaBattleInfo(role, score, addScore, rivalScore, subScore);
//        sendArenaInfo(role);
//        log.info("竞技场-挑战-战斗成功,roleId:{},roleName:{},rivalRoleId:{},rivalRoleName:{}", role.getId(), role.getName(), rivalId, name);
//
//        // 给前端发送奖励消息
//        RewardInfoManager.getInstance().resRewardInfo(role, TurnBasedConst.BattleType.ARENA, reward);
//        GoalManager.getInstance().updateGoal(role, GoalConst.GoalType.ARENA_FIGHT);
    }

    @Override
    public void reqArenaRivalInfo(Role role) {
        // 验证玩家是否解锁
        if (!isUnlockArena(role.getRoleId())) {
            log.error("竞技场-请求对手列表-玩家未解锁竞技场,roleId:{},roleName:{}", role.getId(), role.getName());
            return;
        }

        // 发送对手信息
        sendArenaRivalInfo(role);
    }

    @Override
    public void reqArenaRivalRefresh(Role role) {
        // 验证玩家是否解锁
        if (!isUnlockArena(role.getRoleId())) {
            log.error("竞技场-刷新对手-玩家未解锁竞技场,roleId:{},roleName:{}", role.getId(), role.getName());
            return;
        }

        // 刷新消耗
        List<int[]> itemCost = GlobalUtil.findItemCost(GameConst.GlobalId.ARENA_REFRESH_COST);
        BackpackStash stash = new BackpackStash(role);
        stash.decrease(itemCost);
        if (!stash.commit(role, LogAction.ARENA_REFRESH_COST)) {
            log.error("竞技场-刷新对手-材料不足,roleId:{},roleName:{}", role.getId(), role.getName());
            return;
        }

        // 刷新对手列表
        refreshRival(role);
    }

    private void refreshRival(Role role) {
        refreshRival(role, false);
    }

    private void refreshRival(Role role, boolean isUnlock) {
        ArenaRankData arenaRankData = SysDataProvider.get(ArenaRankData.class);
        int score = arenaRankData.getScore(role.getId());
        List<Long> rankList = arenaRankData.getRankList(role.getId(), score + 20, score - 20);
        if (isUnlock) { //解锁刷新玩家只能刷到机器人
            rankList.removeIf(rid -> !arenaRankData.getRobotMap().containsKey(rid));
        }
        List<Long> roleIds;
        if (rankList.size() >= ArenaConst.RIVAL_NUM) {
            roleIds = RandomUtil.randomElements(rankList, ArenaConst.RIVAL_NUM);
        } else {
            roleIds = arenaRankData.getRankNeighborList(role.getId(), score, ArenaConst.RIVAL_NUM);
        }
        RoleArena roleArena = role.findArena();
        roleArena.setRivals(new HashSet<>(roleIds));
        DataCenter.updateData(roleArena);

        // 发送对手信息
        sendArenaRivalInfo(role);
        log.info("竞技场-刷新对手列表,roleId:{},roleName:{},是否首次解锁刷新:{}", role.getId(), role.getName(), isUnlock);
    }

    @Override
    public void reqArenaLogInfo(Role role) {
//        // 验证玩家是否解锁
//        if (!isUnlockArena(role.getRoleId())) {
//            log.error("竞技场-玩家未解锁竞技场,roleId:{},roleName:{}", role.getId(), role.getName());
//            return;
//        }
//
//        // 发送竞技场日志信息
//        RoleArena roleArena = role.findArena();
//        List<ArenaLog> battleLogList = getBattleLog(roleArena);
//        List<ArenaLogBean> arenaLogBeanList = new ArrayList<>();
//        battleLogList.forEach(battleLog -> {
//            ArenaLogBean builder = new ArenaLogBean();
//            builder.setBattleId(battleLog.getBattleId());
//            builder.setAttackerWin(battleLog.isAttackerWin());
//            builder.setAttackerId(battleLog.getAttackerId());
//            builder.setDefenderId(battleLog.getDefenderId());
//            builder.setScoreChange(battleLog.getScoreChange());
//            builder.setTime(battleLog.getTime());
//            builder.setChallenge(battleLog.isChallenge());
//            builder.setDefenderPower(battleLog.getDefenderPower());
//            builder.setAttackPower(battleLog.getAttackPower());
//            RoleSummary attackerSummary = SummaryManager.getInstance().getSummary(battleLog.getAttackerId());
//            if (attackerSummary != null && attackerSummary.getData() != null) {
//                List<CommonSlotBean> commonSlotBeans = QueryManager.getInstance().fashionsToBean(attackerSummary.getData().getFashions());
//                builder.getAttackerFashions().addAll(commonSlotBeans);
//            }
//            RoleSummary defenderSummary = SummaryManager.getInstance().getSummary(battleLog.getDefenderId());
//            if (defenderSummary != null && defenderSummary.getData() != null) {
//                List<CommonSlotBean> commonSlotBeans = QueryManager.getInstance().fashionsToBean(defenderSummary.getData().getFashions());
//                builder.addAllDefenderFashions(commonSlotBeans);
//            }
//            arenaLogBeanList.add(builder.build());
//        });
//        ArenaProtos.ResArenaLogInfoMessage resArenaLogInfoMessage = ArenaProtos.ResArenaLogInfoMessage.newBuilder()
//                .addAllArenaLogBean(arenaLogBeanList)
//                .build();
//
//        ResArenaLogInfoMessage msg = new ResArenaLogInfoMessage();
//        msg.setProto(resArenaLogInfoMessage);
//
//        MessageUtil.sendMsg(msg, role.getId());
    }

    @Override
    public void cleanArena(Role role) {
        ArenaRankData arenaRankData = SysDataProvider.get(ArenaRankData.class);
        int now = TimeUtil.getNowOfSeconds();
        arenaRankData.setLastSettleTime(now);
        // 清理排行榜
        arenaRankData.getRankMap().clear();
        arenaRankData.getScoreRankMap().clear();
        arenaRankData.getRobotMap().clear();
        arenaRankData.setInitRobot(false);
        initRobot();
        // 更新对手
        refreshRival(role);
        DataCenter.updateData(arenaRankData);
    }

    @Override
    public void scheduleUpdateOnMidnight() {
        // 结算（每日结算和每周结算）
        settle();
    }

    @Override
    public void scheduleUpdateOnHour() {
        ArenaRankData arenaRankData = SysDataProvider.get(ArenaRankData.class);
        if (arenaRankData.isInitRobot() && arenaRankData.getLastSettleTime() > 0) {
            return;
        }
        if (!arenaRankData.isInitRobot()) {
            initRobot();
        }
        if (arenaRankData.getLastSettleTime() == 0) {
            arenaRankData.setLastSettleTime(TimeUtil.getNowOfSeconds());
        }
        DataCenter.updateData(arenaRankData);
    }

    private void settle() {
        ArenaRankData arenaRankData = SysDataProvider.get(ArenaRankData.class);

        // 日结算
        sendRankReward(ArenaConst.RANK_TYPE_DAY, arenaRankData.getScoreRankMap(), LogAction.ARENA_RANK_DAY_REWARD);
        // 周结算
        int todayOfWeek = TimeUtil.todayOfWeek();
        int now = TimeUtil.getNowOfSeconds();
        int lastSettleTime = arenaRankData.getLastSettleTime();
        int[] weekArr = GlobalUtil.findJingHaoIntArray(GameConst.GlobalId.ARENA_RESET_TIME);
        int day = 1;
        int betweenDay = 4;
        if (weekArr.length > 1) {
            day = weekArr[0];
            betweenDay = weekArr[1];
        }
        if (todayOfWeek == day && TimeUtil.betweenDay(lastSettleTime * 1000L, now * 1000L) >= betweenDay) {
            arenaRankData.setLastSettleTime(now);
            // 周结算
            sendRankReward(ArenaConst.RANK_TYPE_WEEK, arenaRankData.getScoreRankMap(), LogAction.ARENA_RANK_WEEK_REWARD);
            // 清理排行榜
            arenaRankData.getRankMap().clear();
            arenaRankData.getScoreRankMap().clear();
            arenaRankData.getRobotMap().clear();
            arenaRankData.setInitRobot(false);
        }

        if (!arenaRankData.isInitRobot()) {
            initRobot();
        }

        DataCenter.updateData(arenaRankData);
    }

    private void sendRankReward(int rankType, ConcurrentSkipListMap<Long, Long> scoreRankMap, LogAction logAction) {
        ArenaRankConfigCache cache = ConfigCacheManager.getInstance().getCache(ArenaRankConfigCache.class);
        ArenaRankData arenaRankData = SysDataProvider.get(ArenaRankData.class);
        int rank = 0;
        HashSet<Long> rewardSet = new HashSet<>();
        for (Long rid : scoreRankMap.values()) {
            //防止多发
            if (rewardSet.contains(rid)) {
                continue;
            }
            rewardSet.add(rid);
            rank++;
            ArenaRoleData arenaRoleData = arenaRankData.getRankMap().getOrDefault(rid, null);
            ArenaRankConfig arenaRankConfig = cache.getArenaRankConfig(rankType, rank);
            if (arenaRankConfig == null) {
                log.error("竞技场-发送排行奖励-配置不存在,roleId:{},rank:{}", rid, rank);
                continue;
            }
            if (arenaRoleData == null) {
                continue;
            }
            if (arenaRankData.getRobotMap().containsKey(rid)) {
                log.warn("竞技场-发送排行奖励-机器人跳过发奖,roleId:{},rank:{}", rid, rank);
                continue;
            }
            log.info("竞技场-发送排行奖励,roleId:{},rank:{},score:{},rankType:{}", rid, rank, arenaRoleData.getScore(), rankType);
            MailManager.getInstance().sendMail(rid, arenaRankConfig.getMailId(), EmailConst.toMailAttach(arenaRankConfig.getReward(), logAction));
        }
    }

    private boolean isInSettleTime(int now) {
        int dayZeroSecondsFromNow = TimeUtil.dayZeroSecondsFromNow();
        int todayOfSeconds = now - dayZeroSecondsFromNow;
        int todayOfWeek = TimeUtil.todayOfWeek();
        int arenaSettleTime;
        if (todayOfWeek != 7) {
            arenaSettleTime = GlobalUtil.getGlobalInt(GameConst.GlobalId.ARENA_DAY_SETTLE_TIME);
        } else {
            arenaSettleTime = GlobalUtil.getGlobalInt(GameConst.GlobalId.ARENA_WEEK_SETTLE_TIME);
        }
        int openTime = GlobalUtil.getGlobalInt(GameConst.GlobalId.ARENA_DAY_OPEN_TIME);
        return todayOfSeconds > arenaSettleTime || todayOfSeconds < openTime;
    }

    private void sendArenaInfo(Role role) {
//        ArenaRankData arenaRankData = SysDataProvider.get(ArenaRankData.class);
//        List<ArenaRoleData> headRankList = arenaRankData.getHeadRankList(ArenaConst.HEAD_NUM);
//        List<ArenaProtos.ArenaRoleBean> arenaRoleBeanList = new ArrayList<>();
//        int rank = 1;
//        for (ArenaRoleData arenaRoleData : headRankList) {
//            arenaRoleBeanList.add(buildArenaRoleBean(arenaRankData, arenaRoleData, rank));
//            rank++;
//        }
//
//        ArenaProtos.ResArenaInfoMessage resArenaInfoMessage = ArenaProtos.ResArenaInfoMessage.newBuilder()
//                .addAllArenaRoleBean(arenaRoleBeanList)
//                .setScore(arenaRankData.getScore(role.getId()))
//                .setRank(arenaRankData.getRank(role.getId()))
//                .setSuccessCount(role.findArenaSuccessCount())
//                .build();
//
//        ResArenaInfoMessage msg = new ResArenaInfoMessage();
//        msg.setProto(resArenaInfoMessage);
//
//        MessageUtil.sendMsg(msg, role.getId());
    }

    private void sendArenaRivalInfo(Role role) {
//        ArenaRankData arenaRankData = SysDataProvider.get(ArenaRankData.class);
//        RoleArena roleArena = role.findArena();
//        List<ArenaRoleData> headRankList = arenaRankData.getArenaRoleDataList(roleArena.getRivals());
//        List<ArenaProtos.ArenaRoleBean> arenaRoleBeanList = new ArrayList<>();
//        for (ArenaRoleData arenaRoleData : headRankList) {
//            arenaRoleBeanList.add(buildArenaRoleBean(arenaRankData, arenaRoleData, 0));
//        }
//        ArenaProtos.ResArenaRivalInfoMessage resArenaInfoMessage = ArenaProtos.ResArenaRivalInfoMessage.newBuilder()
//                .addAllArenaRivalBean(arenaRoleBeanList)
//                .build();
//
//        ResArenaRivalInfoMessage msg = new ResArenaRivalInfoMessage();
//        msg.setProto(resArenaInfoMessage);
//
//        MessageUtil.sendMsg(msg, role.getId());
    }
//
//    private ArenaProtos.ArenaRoleBean buildArenaRoleBean(ArenaRankData arenaRankData, ArenaRoleData arenaRoleData, int rank) {
//        ArenaProtos.ArenaRoleBean.Builder roleBuilder = ArenaProtos.ArenaRoleBean.newBuilder();
//        roleBuilder.setRoleId(arenaRoleData.getRoleId());
//        roleBuilder.setScore(arenaRoleData.getScore());
//        roleBuilder.setRank(rank);
//        if (arenaRankData.getRobotMap().containsKey(arenaRoleData.getRoleId())) {
//            MGRobotData robotData = arenaRankData.getRobotMap().get(arenaRoleData.getRoleId());
//            ArenaProtos.ArenaRobotBean.Builder robotBuilder = ArenaProtos.ArenaRobotBean.newBuilder();
//            robotBuilder.setRobotCid(robotData.getCfgId());
//            robotBuilder.setUseMount(robotData.getMountId());
//            List<AbcProtos.CommonSlotBean> commonSlotBeans = QueryManager.getInstance().fashionsToBean(robotData.getFashions());
//            robotBuilder.addAllRobotFashions(commonSlotBeans);
//            roleBuilder.setRobot(robotBuilder.build());
//        } else {
//            RoleSummary summary = SummaryManager.getInstance().getSummary(arenaRoleData.getRoleId());
//            if (summary != null && summary.getData() != null) {
//                List<AbcProtos.CommonSlotBean> commonSlotBeans = QueryManager.getInstance().fashionsToBean(summary.getData().getFashions());
//                roleBuilder.addAllFashions(commonSlotBeans);
//            }
//        }
//        return roleBuilder.build();
//    }
//
//    private void sendArenaBattleInfo(Role role, int score, int scoreChange, int rivalScore, int rivalScoreChannge) {
//        ArenaProtos.ResArenaBattleMessage resArenaBattleMessage = ArenaProtos.ResArenaBattleMessage.newBuilder()
//                .setScore(score)
//                .setScoreChange(scoreChange)
//                .setRivalScore(rivalScore)
//                .setRivalScoreChange(rivalScoreChannge)
//                .build();
//        ResArenaBattleMessage msg = new ResArenaBattleMessage();
//        msg.setProto(resArenaBattleMessage);
//
//        MessageUtil.sendMsg(msg, role.getId());
//    }

    private boolean isUnlockArena(long roleId) {
        ArenaRankData arenaRankData = SysDataProvider.get(ArenaRankData.class);
        return arenaRankData.isInRank(roleId);
    }

    private void unlockArena(Role role) {
        ArenaRankData arenaRankData = SysDataProvider.get(ArenaRankData.class);
        arenaRankData.addRank(role.getId(), ArenaConst.INIT_SCORE, TimeUtil.getNowOfSeconds());
        refreshRival(role, true);
        sendArenaInfo(role);
        log.info("竞技场-玩家解锁竞技场结束,roleId:{},roleName:{}", role.getId(), role.getName());
    }

    private void addBattleLog(Role role, long battleId, boolean success, long attackerId, long defenderId, int subScore, int nowOfSeconds, long attackPower, long defendPower) {
        ArenaLog arenaLog = new ArenaLog();
        arenaLog.setBattleId(battleId);
        arenaLog.setAttackerWin(success);
        arenaLog.setAttackerId(attackerId);
        arenaLog.setDefenderId(defenderId);
        arenaLog.setScoreChange(subScore);
        arenaLog.setTime(nowOfSeconds);
        arenaLog.setChallenge(false);
        arenaLog.setAttackPower(attackPower);
        arenaLog.setDefenderPower(defendPower);

        RoleArena roleArena = role.findArena();
        Map<Long, ArenaLog> battleLog = roleArena.getBattleLog();
        battleLog.put(battleId, arenaLog);
        DataCenter.updateData(roleArena);
    }

    private List<ArenaLog> getBattleLog(RoleArena roleArena) {
        Map<Long, ArenaLog> battleLog = roleArena.getBattleLog();
        return battleLog.values().stream().sorted(Comparator.comparing(ArenaLog::getTime)).collect(Collectors.toList());
    }

    @Override
    public void onRoleMidnight(Role role) {
        RoleArena arena = role.findArena();
        long lastLogRestTime = arena.getLastLogRestTime();
        if (lastLogRestTime == 0) {
            arena.setLastLogRestTime(TimeUtil.getNowOfMills());
            return;
        }
        if (TimeUtil.isSameWeek(lastLogRestTime)) {
            return;
        }
        arena.setLastLogRestTime(TimeUtil.getNowOfMills());
        arena.getBattleLog().clear();
        arena.getRivals().clear();
        DataCenter.updateData(arena);
    }

    private void initRobot() {
        log.info("竞技场#开始初始化机器人");
        ArenaRankData arenaRankData = SysDataProvider.get(ArenaRankData.class);
        for (ArenaRobotConfig config : ConfigDataManager.getInstance().getList(ArenaRobotConfig.class)) {
            RoBotConfig roBotConfig = ConfigDataManager.getInstance().getById(RoBotConfig.class, config.getRobotID());
            if (roBotConfig == null) {
                continue;
            }
            long robotId = IDUtil.getId(IDConst.PERSISTENT);
            arenaRankData.addRank(robotId, config.getScore(), TimeUtil.getNowOfSeconds());
            MGRobotData robotData = createRobot(robotId, roBotConfig);
            arenaRankData.getRobotMap().put(robotId, robotData);
        }
        //刷新排行榜
        arenaRankData.loadScoreRank();
        arenaRankData.setInitRobot(true);
        log.info("竞技场#初始化机器人成功");
    }

    private MGRobotData createRobot(long robotId, RoBotConfig config) {
        MGRobotData robotData = new MGRobotData();
        robotData.setId(robotId);
        robotData.setCfgId(config.getId());
        List<Integer> randHorse = config.getRandHorse();
        if (CollectionUtils.isNotEmpty(randHorse)) {
            Integer mountId = RandomUtil.randomElement(randHorse);
            if (mountId != null) {
                robotData.setMountId(mountId);
            }
        }
        robotData.setFashions(QueryManager.getInstance().fashionsToMap(config.getFashion()));
        return robotData;
    }

    @Override
    public void onSeverStartUp() {
        ArenaRankData arenaRankData = SysDataProvider.get(ArenaRankData.class);
        arenaRankData.loadScoreRank();
    }
}
