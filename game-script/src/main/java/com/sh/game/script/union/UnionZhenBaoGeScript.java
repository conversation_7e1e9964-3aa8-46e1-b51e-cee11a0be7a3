package com.sh.game.script.union;

import com.alibaba.fastjson.JSON;
import com.sh.common.config.ConfigCacheManager;
import com.sh.common.config.ConfigDataManager;
import com.sh.game.common.communication.msg.system.union.ResUnionZhenBaoGeInfoMessage;
import com.sh.game.common.communication.msg.system.union.bean.ZhenBaoGeInfoBean;
import com.sh.game.common.config.cache.GlobalConfigChace;
import com.sh.game.common.config.model.UnionConfig;
import com.sh.game.common.config.model.UnionTreasureBarginConfig;
import com.sh.game.common.config.model.UnionTreasureGoodsConfig;
import com.sh.game.common.constant.*;
import com.sh.game.common.entity.Union;
import com.sh.game.common.entity.backpack.BackpackStash;
import com.sh.game.common.entity.backpack.item.Item;
import com.sh.game.common.entity.usr.Role;
import com.sh.game.common.tips.CommonTips;
import com.sh.game.common.util.*;
import com.sh.game.common.util.condition.ConditionUtil;
import com.sh.game.data.DataCenter;
import com.sh.game.event.IEventScheduleUpdateOnMidnightScript;
import com.sh.game.system.count.CountManager;
import com.sh.game.system.mail.MailManager;
import com.sh.game.system.rewardinfo.RewardInfoManager;
import com.sh.game.system.union.UnionManager;
import com.sh.game.system.union.entity.MemberInfo;
import com.sh.game.system.union.entity.UnionZhenBaoGe;
import com.sh.game.system.union.entity.ZhenBaoGeLog;
import com.sh.game.system.union.script.IUnionZhenBaoGeScript;

import com.sh.script.annotation.Script;
import lombok.extern.slf4j.Slf4j;

import java.util.*;

/**
 * <AUTHOR>
 * @date 2023/11/28 15:17
 */
@Script
@Slf4j
public class UnionZhenBaoGeScript implements IUnionZhenBaoGeScript, IEventScheduleUpdateOnMidnightScript {

    @Override
    public void scheduleUpdateOnMidnight() {
        for (Union union : DataCenter.getAllUnion()) {
            initZhenBaoGe(union);
        }
    }

    @Override
    public void info(Role role) {
        Union union = UnionManager.getInstance().getUnion(role);
        if (union == null) {
            return;
        }
        ResUnionZhenBaoGeInfoMessage msg = new ResUnionZhenBaoGeInfoMessage();
        UnionZhenBaoGe zhenBaoGe = union.getZhenBaoGe();
        msg.setGoodsId(zhenBaoGe.getGoodsId());
        msg.setNowPrice(zhenBaoGe.getNowPrice());
        msg.setYiJia(CountManager.getInstance().getCount(role, CountConst.CountType.ZHEN_BAO_GE_YI_JIA));
        msg.setBuy(CountManager.getInstance().getCount(role, CountConst.CountType.ZHEN_BAO_GE_BUY));
        msg.setYiJiaMax(zhenBaoGe.getYiJiaMax());
        msg.setYiJiaCurCount(zhenBaoGe.getCurYiJiaCount());
        Map<Long, ZhenBaoGeLog> kanJiaMap = zhenBaoGe.getKanJiaMap();
        for (MemberInfo info : union.getMemberInfos().values()) {
            long memberId = info.getMemberId();
            Role member = DataCenter.get(Role.class, memberId);
            if (member == null) {
                continue;
            }
            ZhenBaoGeLog zhenBaoGeLog = kanJiaMap.get(memberId);
            ZhenBaoGeInfoBean b = new ZhenBaoGeInfoBean();
            b.setRid(memberId);
            b.setName(member.getName());
            if (zhenBaoGeLog != null) {
                b.setBuy(zhenBaoGeLog.getBuy());
                b.setYiJia(1);
                b.setMinusPrice(zhenBaoGeLog.getMinusPrice());
            }
            msg.getInfos().add(b);
        }
        for (Map.Entry<Long, ZhenBaoGeLog> entry : kanJiaMap.entrySet()) {
            // 退出行会的玩家的议价记录也要显示
            Long memberId = entry.getKey();
            if (union.getMemberInfos().containsKey(memberId)) {
                continue;
            }
            ZhenBaoGeLog zhenBaoGeLog = entry.getValue();
            ZhenBaoGeInfoBean b = new ZhenBaoGeInfoBean();
            b.setRid(memberId);
            b.setName(role.getName());
            b.setBuy(zhenBaoGeLog.getBuy());
            b.setYiJia(1);
            b.setMinusPrice(zhenBaoGeLog.getMinusPrice());
            msg.getInfos().add(b);
        }

        MessageUtil.sendMsg(msg, role);
    }

    @Override
    public void yiJia(Role role) {
        if (!zhenBaoGeOpen()) {
            return;
        }
        int count = CountManager.getInstance().getCount(role, CountConst.CountType.ZHEN_BAO_GE_YI_JIA);
        if (count > 0) {
            TipUtil.show(role.getId(), CommonTips.珍宝阁_今日无剩余砍价次数);
            log.error("珍宝阁，玩家请求议价，玩家今日已议价，玩家id:{},name:{}", role.getId(), role.getName());
            return;
        }
        Union union = UnionManager.getInstance().getUnion(role);
        if (union == null) {
            return;
        }
        UnionZhenBaoGe zhenBaoGe = union.getZhenBaoGe();
        if (zhenBaoGe.getCurYiJiaCount() >= zhenBaoGe.getYiJiaMax()) { //超过最大砍价次数
            return;
        }

        int minusPrice;
        int nowPrice = zhenBaoGe.getNowPrice();
        int minPrice = zhenBaoGe.getMinPrice();
        if (nowPrice > minPrice) {
            GlobalConfigChace cache = ConfigCacheManager.getInstance().getCache(GlobalConfigChace.class);
            int[] kanJiaParam = cache.getZhenBaoGeKanJiaParam();
            if (kanJiaParam == null || kanJiaParam.length < 2) {
                log.error("珍宝阁，玩家请求议价，议价参数配置错误，玩家id:{},name:{},行会id:{},name:{}", role.getId(), role.getName(), union.getId(), union.getName());
                return;
            }
            int randomRate = RandomUtil.random(kanJiaParam[0], kanJiaParam[1]);
            double realRate = randomRate / 10000.0;
            minusPrice = (int) Math.ceil((zhenBaoGe.getOriginPrice() - zhenBaoGe.getMinPrice()) * realRate / zhenBaoGe.getYiJiaRate());
        } else {
            int[] arr = GlobalUtil.findJingHaoIntArray(GameConst.GlobalId.ZHEN_BAO_GE_YI_JIA_AFTER_MIN_PRICE);
            if (arr.length >= 2) {
                minusPrice = RandomUtil.random(arr[0], arr[1]);
            } else {
                minusPrice = 1;
            }

        }
        int newPrice = nowPrice - minusPrice;

        ZhenBaoGeLog zhenBaoGeLog = new ZhenBaoGeLog();
        zhenBaoGeLog.setMinusPrice(minusPrice);
        zhenBaoGeLog.setName(role.getName());
        zhenBaoGe.getKanJiaMap().put(role.getId(), zhenBaoGeLog);
        zhenBaoGe.setCurYiJiaCount(zhenBaoGe.getCurYiJiaCount() + 1);

        zhenBaoGe.setNowPrice(newPrice);
        DataCenter.updateData(union);
        log.info("珍宝阁，玩家请求议价，玩家id:{},name:{},行会id:{},name:{},减少价格:{},新价格:{}", role.getId(), role.getName(), union.getId(), union.getName(), minusPrice, newPrice);
        CountManager.getInstance().count(role, CountConst.CountType.ZHEN_BAO_GE_YI_JIA);
        info(role);
    }

    @Override
    public void buy(Role role) {
        if (!zhenBaoGeOpen()) {
            return;
        }
        int count = CountManager.getInstance().getCount(role, CountConst.CountType.ZHEN_BAO_GE_BUY);
        if (count > 0) {
            log.error("珍宝阁，玩家请求购买，玩家今日已购买，玩家id:{},name:{}", role.getId(), role.getName());
            return;
        }
        Union union = UnionManager.getInstance().getUnion(role);
        if (union == null) {
            return;
        }
        UnionZhenBaoGe zhenBaoGe = union.getZhenBaoGe();
        if (!zhenBaoGe.getKanJiaMap().containsKey(role.getId())) {
            int yiJiaCount = CountManager.getInstance().getCount(role, CountConst.CountType.ZHEN_BAO_GE_YI_JIA);
            if (yiJiaCount > 0) { //在其他行会议价
                log.error("珍宝阁，玩家请求购买，玩家已在其他行会议价，玩家id:{},name:{},行会id:{},name:{}", role.getId(), role.getName(), union.getId(), union.getName());
                return;
            } else if (zhenBaoGe.getCurYiJiaCount() < zhenBaoGe.getYiJiaMax()) {//联盟议价次数未到上限且未议价
                log.error("珍宝阁，玩家请求购买，玩家尚未议价，玩家id:{},name:{},行会id:{},name:{}", role.getId(), role.getName(), union.getId(), union.getName());
                return;
            }
        }

        int goodsId = zhenBaoGe.getGoodsId();
        UnionTreasureGoodsConfig config = ConfigDataManager.getInstance().getById(UnionTreasureGoodsConfig.class, goodsId);
        if (config == null) {
            log.error("珍宝阁，玩家请求购买，找不到商品id，玩家id:{},name:{},行会id:{},name:{}", role.getId(), role.getName(), union.getId(), union.getName());
            return;
        }

        int nowPrice = zhenBaoGe.getNowPrice();
        BackpackStash add = new BackpackStash(role);
        List<Item> items = EmailConst.toMailAttach(config.getGoods(), LogAction.ZHEN_BAO_GE);
        if (nowPrice > 0) {
            BackpackStash stash = new BackpackStash(role);
            stash.decrease(config.getCurrency(), nowPrice);
            if (!stash.commit(role, LogAction.ZHEN_BAO_GE)) {
                return;
            }
        } else {
            Item item = ItemUtil.create(config.getCurrency(), -nowPrice, LogAction.ZHEN_BAO_GE);
            items.add(item);
        }
        CountManager.getInstance().count(role, CountConst.CountType.ZHEN_BAO_GE_BUY);
        add.increase(items);
        if (!add.commit(role, LogAction.ZHEN_BAO_GE)) {
            MailManager.getInstance().sendMail(role.getId(), EmailConst.MailId.BACK_OVER_FLOW, items);
        }
        RewardInfoManager.getInstance().resRewardInfoByItem(role, RewardInfoTypeConst.NORMAL, items);
        ZhenBaoGeLog zhenBaoGeLog = zhenBaoGe.getKanJiaMap().get(role.getId());
        zhenBaoGeLog.setBuy(1);

        DataCenter.updateData(union);
        info(role);
        log.info("珍宝阁，玩家请求购买，玩家id:{},name:{},行会id:{},name:{},商品id:{},购买价格:{}", role.getId(), role.getName(), union.getId(), union.getName(), goodsId, nowPrice);
    }

    @Override
    public void initZhenBaoGe(Union union) {
        UnionZhenBaoGe zhenBaoGe = union.getZhenBaoGe();
        int bargainId = zhenBaoGe.getBargainId();
        if (bargainId == 0) {
            bargainId = 1;
        }
        UnionTreasureBarginConfig config = ConfigDataManager.getInstance().getById(UnionTreasureBarginConfig.class, bargainId);
        if (config == null) {
            log.error("珍宝阁，找不到珍宝阁配置，行会id:{},name:{},配置id:{}", union.getId(), union.getName(), bargainId);
            return;
        }
        if (!ConditionUtil.validate(config.getCondition())) {
            config = ConfigDataManager.getInstance().getById(UnionTreasureBarginConfig.class, config.getNextID());
            if (config == null) {
                log.error("珍宝阁，找不到珍宝阁配置，行会id:{},name:{},配置id:{}", union.getId(), union.getName(), bargainId);
                return;
            }
        }
        if (zhenBaoGe.getUsedGoodIds().containsAll(config.getGoodsId())) {
            config = ConfigDataManager.getInstance().getById(UnionTreasureBarginConfig.class, config.getNextID());
            if (config == null) {
                log.error("珍宝阁，找不到珍宝阁配置，行会id:{},name:{},配置id:{}", union.getId(), union.getName(), bargainId);
                return;
            }
        }
        if (config.getId() != bargainId) {
            log.info("珍宝阁，更换行会珍宝阁id，行会id:{},name:{},老id:{},新id:{}", union.getId(), union.getName(), bargainId, config.getId());
            zhenBaoGe.getUsedGoodIds().clear();
            zhenBaoGe.getUsedMinPrice().clear();
            zhenBaoGe.setBargainId(config.getId());
        }

        List<Integer> goodList = new ArrayList<>(config.getGoodsId());
        goodList.removeAll(zhenBaoGe.getUsedGoodIds());
        if (goodList.isEmpty()) {
            log.error("珍宝阁，随机商品错误，行会id:{},name:{},配置id:{},行会已用商品id:{}",
                    union.getId(), union.getName(), config.getId(), JSON.toJSONString(zhenBaoGe.getUsedGoodIds()));
            return;
        }
        Integer goodId = RandomUtil.randomElement(goodList);
        if (goodId == null) {
            log.error("珍宝阁，随机商品错误，行会id:{},name:{},配置id:{},行会已用商品id:{}",
                    union.getId(), union.getName(), config.getId(), JSON.toJSONString(zhenBaoGe.getUsedGoodIds()));
            return;
        }
        UnionTreasureGoodsConfig goodsConfig = ConfigDataManager.getInstance().getById(UnionTreasureGoodsConfig.class, goodId);
        if (goodsConfig == null) {
            log.error("珍宝阁，找不到珍宝阁商品配置，行会id:{},name:{},商品配置id:{}", union.getId(), union.getName(), goodId);
            return;
        }
        zhenBaoGe.setGoodsId(goodId);
        zhenBaoGe.getUsedGoodIds().add(goodId);
        zhenBaoGe.setOriginPrice(goodsConfig.getPrice());
        zhenBaoGe.setNowPrice(goodsConfig.getPrice());
        resetZBG(union);

        List<Integer> minPriceList = new ArrayList<>(config.getMinimum_price());
        minPriceList.removeAll(zhenBaoGe.getUsedMinPrice());
        if (minPriceList.isEmpty()) {
            log.warn("珍宝阁，最低价格已全部使用，行会id:{},name:{},配置id:{},行会已用商品最低价格:{}",
                    union.getId(), union.getName(), config.getId(), JSON.toJSONString(zhenBaoGe.getUsedMinPrice()));
            return;
        }
        Integer minPrice = RandomUtil.randomElement(minPriceList);
        if (minPrice == null) {
            log.warn("珍宝阁，未随机到商品最低价格，行会id:{},name:{},配置id:{},行会已用商品最低价格:{}",
                    union.getId(), union.getName(), config.getId(), JSON.toJSONString(zhenBaoGe.getUsedMinPrice()));
            return;
        }
        zhenBaoGe.setMinPrice(minPrice);
        DataCenter.updateData(union);

        log.info("珍宝阁，每日重置成功，行会id:{},name:{},珍宝阁id:{},商品id:{},最低价格:{},原价:{},已使用商品id:{},已使用最低价格:{}",
                union.getId(), union.getName(), zhenBaoGe.getBargainId(), zhenBaoGe.getGoodsId(), zhenBaoGe.getMinPrice(),
                zhenBaoGe.getOriginPrice(), JSON.toJSONString(zhenBaoGe.getUsedGoodIds()), JSON.toJSONString(zhenBaoGe.getUsedMinPrice()));
    }

    /**
     * 重置珍宝阁部分数据
     *
     * @param union union
     */
    private void resetZBG(Union union) {
        UnionZhenBaoGe zhenBaoGe = union.getZhenBaoGe();
        UnionConfig unionConfig = ConfigDataManager.getInstance().getById(UnionConfig.class, union.getUnionLevel());
        zhenBaoGe.setYiJiaMax(unionConfig.getMember());
        zhenBaoGe.setCurYiJiaCount(0);
        int rate = GlobalUtil.getGlobalInt(GameConst.GlobalId.ZHEN_BAO_GE_KAN_JIA_MEMBER_COUNT_RATE);
        rate = Math.max(rate, 1);
        zhenBaoGe.setYiJiaRate(unionConfig.getMember() * rate / 10000.0);
        zhenBaoGe.getKanJiaMap().clear();
        DataCenter.updateData(union);
    }

    private boolean zhenBaoGeOpen() {
        int open = GlobalUtil.getGlobalInt(GameConst.GlobalId.ZHEN_BAO_GE_OPEN_TIME);
        int end = GlobalUtil.getGlobalInt(GameConst.GlobalId.ZHEN_BAO_GE_END_TIME);
        int now = TimeUtil.getTodayOfSeconds();
        return now >= open && now <= end;
    }
}
